# Forward iOS应用完整功能规格书

## 应用概述
Forward是一款由AI驱动的下一代视频播放器和媒体库管理工具，旨在彻底改变用户管理、组织和体验媒体的方式。

## 核心功能分类

### 1. 核心播放功能 ⭐⭐⭐ (核心)

#### 1.1 视频格式支持
- **支持格式**: MKV, AVI, MP4, MOV, WMV, FLV, MPEG, 3GP等主流格式
- **编码支持**: H.264, H.265/HEVC, VP9, AV1等
- **容器支持**: MP4, MKV, AVI, MOV, WMV, FLV等
- **技术实现**: AVFoundation + 自研解码内核
- **开发复杂度**: 高 (需要自定义解码器)
- **开发时间**: 4-6周

#### 1.2 高画质支持
- **HDR支持**: HDR10, Dolby Vision, Dovi P5
- **分辨率**: 4K UHD, 8K支持
- **帧率**: 支持120fps高帧率播放
- **色彩空间**: Rec.2020, DCI-P3, sRGB
- **技术实现**: AVPlayerItem.videoComposition, Metal渲染
- **开发复杂度**: 高
- **开发时间**: 3-4周

#### 1.3 音频支持
- **格式**: AAC, MP3, FLAC, DTS, AC3, EAC3
- **多声道**: 7.1环绕声, Dolby Atmos
- **空间音频**: 支持Apple空间音频
- **技术实现**: AVAudioEngine, Core Audio
- **开发复杂度**: 中等
- **开发时间**: 2-3周

#### 1.4 播放控制
- **基础控制**: 播放/暂停, 快进/快退, 音量调节
- **高级控制**: 倍速播放(0.5x-3x), A-B循环, 逐帧播放
- **手势控制**: 滑动调节进度, 双击暂停/播放, 捏合缩放
- **技术实现**: AVPlayer + 自定义UI控件
- **开发复杂度**: 中等
- **开发时间**: 2-3周

### 2. AI智能功能 ⭐⭐⭐ (核心)

#### 2.1 智能媒体库管理
- **自动分类**: AI识别电影、剧集、季、集信息
- **路径智能识别**: 根据文件夹结构自动判断内容类型
- **重复检测**: 智能识别重复文件
- **技术实现**: Core ML + 自然语言处理
- **开发复杂度**: 高
- **开发时间**: 6-8周

#### 2.2 元数据刮削
- **数据源**: TMDB (The Movie Database)
- **自动匹配**: 中英文影片名智能匹配
- **元数据获取**: 封面、简介、演员、评分、预告片
- **批量处理**: 支持大量文件批量刮削
- **技术实现**: TMDB API + AI匹配算法
- **开发复杂度**: 中等
- **开发时间**: 3-4周

#### 2.3 智能推荐
- **个性化推荐**: 基于观看历史的AI推荐
- **相似内容**: 推荐相似类型影片
- **趋势分析**: 今日趋势、本周趋势
- **技术实现**: 机器学习推荐算法
- **开发复杂度**: 高
- **开发时间**: 4-6周

### 3. 字幕功能 ⭐⭐ (重要)

#### 3.1 字幕格式支持
- **支持格式**: SRT, SSA, ASS, VTT, SUB等
- **编码支持**: UTF-8, GBK, Big5等多种编码
- **样式支持**: 字体、颜色、大小、位置自定义
- **技术实现**: AVMediaSelectionGroup + 自定义渲染
- **开发复杂度**: 中等
- **开发时间**: 2-3周

#### 3.2 智能字幕功能
- **自动匹配**: 根据影片信息自动下载字幕
- **在线搜索**: 集成字幕网站API
- **同步调整**: 字幕时间轴微调
- **双语字幕**: 支持双语字幕显示
- **技术实现**: 字幕API集成 + 时间轴算法
- **开发复杂度**: 中等
- **开发时间**: 3-4周

### 4. 云端集成功能 ⭐⭐⭐ (核心)

#### 4.1 阿里云盘支持
- **功能**: 直接访问和播放云盘视频
- **限制**: 目前最高支持480P播放 (Pro功能解锁更高画质)
- **缓存**: 支持离线缓存
- **技术实现**: 阿里云盘开放API
- **开发复杂度**: 中等
- **开发时间**: 3-4周

#### 4.2 WebDAV协议支持
- **功能**: 访问NAS或其他共享设备
- **认证**: 支持用户名密码认证
- **协议**: HTTP/HTTPS WebDAV
- **技术实现**: NSURLSession + WebDAV协议实现
- **开发复杂度**: 中等
- **开发时间**: 2-3周

#### 4.3 网络流媒体
- **协议支持**: HTTP, HTTPS, RTMP, HLS
- **缓存策略**: 智能预加载和缓存
- **断点续传**: 支持网络中断后继续播放
- **技术实现**: AVPlayer + 自定义网络层
- **开发复杂度**: 高
- **开发时间**: 4-5周

### 5. 用户界面功能 ⭐⭐ (重要)

#### 5.1 主题和外观
- **设计风格**: 现代简约, 深蓝渐变主题
- **适配**: 支持iOS 18着色图标
- **暗黑模式**: 完整的暗黑模式支持
- **自定义**: 主题色彩自定义
- **技术实现**: SwiftUI + 动态颜色系统
- **开发复杂度**: 中等
- **开发时间**: 2-3周

#### 5.2 交互体验
- **手势操作**: 滑动、点击、长按、捏合等
- **动画效果**: 流畅的转场和交互动画
- **反馈**: 触觉反馈和视觉反馈
- **技术实现**: SwiftUI动画 + UIKit手势
- **开发复杂度**: 中等
- **开发时间**: 3-4周

#### 5.3 多平台适配
- **iPhone**: 完整功能支持
- **iPad**: 专为iPad优化的界面
- **Apple TV**: 遥控器操作优化
- **Mac**: Apple Silicon Mac支持
- **技术实现**: SwiftUI多平台开发
- **开发复杂度**: 高
- **开发时间**: 6-8周

### 6. 高级功能 ⭐ (增强)

#### 6.1 播放列表管理
- **创建播放列表**: 自定义播放列表
- **智能播放列表**: 基于条件自动生成
- **播放模式**: 顺序、随机、单曲循环
- **技术实现**: Core Data + 播放逻辑
- **开发复杂度**: 中等
- **开发时间**: 2-3周

#### 6.2 观看历史
- **历史记录**: 完整的观看历史
- **断点续播**: 记住播放位置
- **统计信息**: 观看时长统计
- **技术实现**: Core Data + UserDefaults
- **开发复杂度**: 低
- **开发时间**: 1-2周

#### 6.3 搜索功能
- **全局搜索**: 搜索所有媒体内容
- **智能搜索**: 支持模糊匹配
- **过滤器**: 按类型、年份、评分过滤
- **技术实现**: Core Data查询 + 搜索算法
- **开发复杂度**: 中等
- **开发时间**: 2-3周

## Pro功能 (付费解锁) 💰

### 订阅模式
- **月订阅**: ¥10/月
- **年订阅**: ¥78/年
- **永久购买**: ¥98-298 (不同优惠价格)

### Pro功能列表
1. **无限次播放** (免费版限制播放次数)
2. **杜比视界支持** (免费版不支持)
3. **空间音频支持** (免费版不支持)
4. **高画质云盘播放** (免费版限制480P)
5. **无广告体验**
6. **高级AI功能** (智能推荐、批量刮削)
7. **云同步** (播放历史、设置同步)

## 技术架构建议

### 核心框架
- **UI框架**: SwiftUI (主要) + UIKit (兼容)
- **播放引擎**: AVFoundation + 自研解码内核
- **数据存储**: Core Data + CloudKit
- **网络层**: URLSession + Alamofire
- **图片加载**: SDWebImage + AsyncImage

### 第三方依赖
- **TMDB API**: 元数据获取
- **阿里云盘SDK**: 云盘集成
- **机器学习**: Core ML + Create ML
- **崩溃分析**: Crashlytics
- **统计分析**: Firebase Analytics

### 开发优先级建议

#### 第一阶段 (MVP - 8-10周)
1. 基础播放功能
2. 基本UI界面
3. 本地文件管理
4. TMDB元数据集成

#### 第二阶段 (核心功能 - 6-8周)
1. AI智能分类
2. 云盘集成 (阿里云盘)
3. 字幕功能
4. 高画质支持

#### 第三阶段 (高级功能 - 4-6周)
1. WebDAV支持
2. 智能推荐
3. 多平台适配
4. Pro功能实现

#### 第四阶段 (优化完善 - 4-6周)
1. 性能优化
2. 用户体验优化
3. Bug修复
4. 功能完善

## 总开发时间估算
- **总计**: 22-30周 (约5.5-7.5个月)
- **团队规模**: 3-5人 (iOS开发2人, 后端1人, UI设计1人, 测试1人)
- **开发成本**: 中高等 (需要AI算法和多媒体处理专业知识)

## 风险评估
1. **技术风险**: 自研解码器开发难度高
2. **API风险**: 依赖第三方API (TMDB, 阿里云盘)
3. **版权风险**: 需要注意内容版权问题
4. **竞争风险**: 与Infuse等成熟产品竞争激烈

## 详细技术实现指南

### Swift/SwiftUI核心组件实现

#### 1. 主界面结构
```swift
struct ContentView: View {
    @StateObject private var mediaLibrary = MediaLibraryManager()
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            DiscoverView()
                .tabItem { Label("发现", systemImage: "house") }
                .tag(0)

            FollowingView()
                .tabItem { Label("追剧", systemImage: "play.rectangle") }
                .tag(1)

            SettingsView()
                .tabItem { Label("设置", systemImage: "gearshape") }
                .tag(2)
        }
        .background(LinearGradient(colors: [.blue.opacity(0.8), .blue.opacity(0.6)],
                                 startPoint: .top, endPoint: .bottom))
    }
}
```

#### 2. AI媒体库管理器
```swift
class MediaLibraryManager: ObservableObject {
    @Published var movies: [Movie] = []
    @Published var tvShows: [TVShow] = []
    @Published var isScanning = false

    private let tmdbService = TMDBService()
    private let aiClassifier = MediaAIClassifier()

    func scanLibrary() async {
        isScanning = true
        defer { isScanning = false }

        // AI智能分类和元数据获取
        let mediaFiles = await getMediaFiles()
        for file in mediaFiles {
            let classification = await aiClassifier.classify(file)
            let metadata = await tmdbService.fetchMetadata(for: classification)
            await updateLibrary(with: metadata)
        }
    }
}
```

#### 3. 视频播放器组件
```swift
struct VideoPlayerView: UIViewControllerRepresentable {
    let url: URL
    @Binding var isPlaying: Bool

    func makeUIViewController(context: Context) -> AVPlayerViewController {
        let controller = AVPlayerViewController()
        let player = AVPlayer(url: url)
        controller.player = player

        // HDR和高画质支持
        if let playerItem = player.currentItem {
            playerItem.preferredForwardBufferDuration = 5.0
            // 配置HDR支持
            configureHDRSupport(for: playerItem)
        }

        return controller
    }

    private func configureHDRSupport(for item: AVPlayerItem) {
        // HDR10和Dolby Vision配置
        item.videoComposition = createHDRVideoComposition()
    }
}
```

### 数据模型设计

#### Core Data实体定义
```swift
// Movie实体
@objc(Movie)
public class Movie: NSManagedObject {
    @NSManaged public var id: Int32
    @NSManaged public var title: String
    @NSManaged public var originalTitle: String?
    @NSManaged public var overview: String?
    @NSManaged public var releaseDate: Date?
    @NSManaged public var posterPath: String?
    @NSManaged public var backdropPath: String?
    @NSManaged public var voteAverage: Double
    @NSManaged public var runtime: Int32
    @NSManaged public var genres: Set<Genre>
    @NSManaged public var filePath: String
    @NSManaged public var watchHistory: WatchHistory?
}

// 观看历史
@objc(WatchHistory)
public class WatchHistory: NSManagedObject {
    @NSManaged public var lastWatchedTime: Double
    @NSManaged public var totalWatchTime: Double
    @NSManaged public var watchCount: Int32
    @NSManaged public var lastWatchedDate: Date
    @NSManaged public var isCompleted: Bool
}
```

### 网络服务实现

#### TMDB API服务
```swift
class TMDBService {
    private let apiKey = "YOUR_TMDB_API_KEY"
    private let baseURL = "https://api.themoviedb.org/3"

    func searchMovie(query: String) async throws -> [MovieSearchResult] {
        let url = URL(string: "\(baseURL)/search/movie?api_key=\(apiKey)&query=\(query)")!
        let (data, _) = try await URLSession.shared.data(from: url)
        let response = try JSONDecoder().decode(MovieSearchResponse.self, from: data)
        return response.results
    }

    func fetchMovieDetails(id: Int) async throws -> MovieDetails {
        let url = URL(string: "\(baseURL)/movie/\(id)?api_key=\(apiKey)&language=zh-CN")!
        let (data, _) = try await URLSession.shared.data(from: url)
        return try JSONDecoder().decode(MovieDetails.self, from: data)
    }
}
```

#### 阿里云盘集成
```swift
class AliyunDriveService {
    private var accessToken: String?

    func authenticate(refreshToken: String) async throws {
        // 阿里云盘OAuth认证流程
        let authURL = "https://auth.aliyundrive.com/v2/account/token"
        // 实现认证逻辑
    }

    func listFiles(parentId: String = "root") async throws -> [AliyunFile] {
        guard let token = accessToken else { throw AuthError.notAuthenticated }

        let url = URL(string: "https://api.aliyundrive.com/v2/file/list")!
        var request = URLRequest(url: url)
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")

        // 实现文件列表获取
        let (data, _) = try await URLSession.shared.data(for: request)
        return try JSONDecoder().decode(AliyunFileListResponse.self, from: data).items
    }
}
```

### AI功能实现

#### 媒体分类AI模型
```swift
class MediaAIClassifier {
    private var model: MLModel?

    init() {
        loadModel()
    }

    private func loadModel() {
        // 加载Core ML模型用于媒体分类
        guard let modelURL = Bundle.main.url(forResource: "MediaClassifier", withExtension: "mlmodelc") else {
            return
        }
        model = try? MLModel(contentsOf: modelURL)
    }

    func classify(_ filePath: String) async -> MediaClassification {
        // 使用AI模型分析文件路径和名称
        let features = extractFeatures(from: filePath)

        // 调用Core ML模型进行分类
        if let model = model,
           let prediction = try? model.prediction(from: features) {
            return parseClassification(prediction)
        }

        // 回退到规则基础的分类
        return ruleBasedClassification(filePath)
    }

    private func extractFeatures(from path: String) -> MLFeatureProvider {
        // 提取文件路径特征用于AI分类
        // 包括文件夹名称、文件名模式、季集信息等
        return MediaFeatures(path: path)
    }
}
```

### 字幕处理实现

#### 字幕解析器
```swift
class SubtitleParser {
    func parseSRT(_ content: String) -> [SubtitleItem] {
        let lines = content.components(separatedBy: .newlines)
        var subtitles: [SubtitleItem] = []
        var currentItem: SubtitleItem?

        for line in lines {
            if line.isEmpty {
                if let item = currentItem {
                    subtitles.append(item)
                    currentItem = nil
                }
            } else if line.range(of: "-->") != nil {
                // 解析时间码
                let timeRange = parseTimeRange(line)
                currentItem?.startTime = timeRange.start
                currentItem?.endTime = timeRange.end
            } else if Int(line) != nil {
                // 字幕序号
                currentItem = SubtitleItem(index: Int(line)!)
            } else {
                // 字幕文本
                currentItem?.text = (currentItem?.text ?? "") + line + "\n"
            }
        }

        return subtitles
    }
}
```

### 性能优化建议

#### 1. 图片缓存优化
```swift
class ImageCacheManager {
    private let cache = NSCache<NSString, UIImage>()
    private let diskCache = DiskCache()

    func loadImage(from url: URL) async -> UIImage? {
        // 内存缓存检查
        if let cachedImage = cache.object(forKey: url.absoluteString as NSString) {
            return cachedImage
        }

        // 磁盘缓存检查
        if let diskImage = await diskCache.image(for: url.absoluteString) {
            cache.setObject(diskImage, forKey: url.absoluteString as NSString)
            return diskImage
        }

        // 网络下载
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            if let image = UIImage(data: data) {
                cache.setObject(image, forKey: url.absoluteString as NSString)
                await diskCache.store(image, for: url.absoluteString)
                return image
            }
        } catch {
            print("Failed to load image: \(error)")
        }

        return nil
    }
}
```

#### 2. 视频预加载策略
```swift
class VideoPreloader {
    private var preloadQueue: [URL] = []
    private var preloadedAssets: [URL: AVAsset] = [:]

    func preloadVideo(url: URL) {
        guard !preloadedAssets.keys.contains(url) else { return }

        let asset = AVAsset(url: url)
        let keys = ["playable", "duration", "tracks"]

        asset.loadValuesAsynchronously(forKeys: keys) { [weak self] in
            DispatchQueue.main.async {
                self?.preloadedAssets[url] = asset
            }
        }
    }

    func getPreloadedAsset(for url: URL) -> AVAsset? {
        return preloadedAssets[url]
    }
}
```

## 测试策略

### 单元测试
- 媒体分类AI准确性测试
- TMDB API集成测试
- 字幕解析功能测试
- 播放器核心功能测试

### 集成测试
- 云盘服务集成测试
- 端到端播放流程测试
- 多平台兼容性测试

### 性能测试
- 大型媒体库扫描性能
- 高分辨率视频播放性能
- 内存使用优化测试

### 用户体验测试
- 界面响应速度测试
- 手势操作流畅性测试
- 多设备同步测试
