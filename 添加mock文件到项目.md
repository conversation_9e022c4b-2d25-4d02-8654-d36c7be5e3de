# 添加mock.json文件到Xcode项目

## 问题说明
当前mock.json文件存在于项目目录中，但可能没有被正确添加到Xcode项目的Bundle中，导致应用运行时无法加载数据。

## 解决方案

### 方法1: 通过Xcode添加文件（推荐）

1. **打开Xcode项目**
   - 打开 `Prism.xcodeproj`

2. **添加mock.json到项目**
   - 在Xcode左侧项目导航器中，右键点击 `Prism` 文件夹
   - 选择 "Add Files to 'Prism'"
   - 导航到 `/Users/<USER>/project/Prism/Prism/Prism/Data/Mock/mock.json`
   - 选择文件并点击 "Add"

3. **确认文件设置**
   - 选中添加的mock.json文件
   - 在右侧属性面板中，确保 "Target Membership" 中的 "Prism" 被勾选
   - 确保 "Add to target" 选项被启用

### 方法2: 拖拽添加

1. **打开Finder**
   - 导航到 `/Users/<USER>/project/Prism/Prism/Prism/Data/Mock/`

2. **拖拽文件**
   - 将 `mock.json` 文件拖拽到Xcode项目导航器中的适当位置
   - 在弹出的对话框中，确保选择：
     - ✅ Copy items if needed
     - ✅ Add to target: Prism

### 验证文件是否正确添加

1. **检查项目导航器**
   - mock.json文件应该出现在Xcode项目导航器中
   - 文件图标应该是正常的（不是灰色的）

2. **检查Build Phases**
   - 选择项目根节点 → Target "Prism" → Build Phases
   - 在 "Copy Bundle Resources" 中应该能看到 mock.json

3. **运行应用测试**
   - 运行应用，查看控制台输出
   - 应该看到 "成功从Bundle加载mock.json" 的日志

## 当前代码改进

已经改进了数据加载逻辑：

```swift
// 增强的数据加载方法
private func loadMockDataFromBundle() -> Data? {
    // 方法1: 从主Bundle加载
    if let url = Bundle.main.url(forResource: "mock", withExtension: "json") {
        print("找到mock.json文件路径: \(url.path)")
        if let data = try? Data(contentsOf: url) {
            print("成功从Bundle加载mock.json")
            return data
        }
    }
    
    // 方法2: 尝试从项目目录加载
    let projectPaths = [
        "Data/Mock/mock.json",
        "Prism/Data/Mock/mock.json", 
        "mock.json"
    ]
    
    for path in projectPaths {
        if let url = Bundle.main.url(forResource: path, withExtension: nil),
           let data = try? Data(contentsOf: url) {
            print("成功从路径加载mock.json: \(path)")
            return data
        }
    }
    
    print("无法找到mock.json文件")
    return nil
}
```

## 故障排除

### 如果仍然无法加载数据：

1. **检查文件路径**
   ```bash
   ls -la /Users/<USER>/project/Prism/Prism/Prism/Data/Mock/
   ```

2. **检查文件权限**
   ```bash
   chmod 644 /Users/<USER>/project/Prism/Prism/Prism/Data/Mock/mock.json
   ```

3. **清理并重新构建**
   - 在Xcode中: Product → Clean Build Folder
   - 然后重新运行项目

4. **检查控制台输出**
   - 运行应用时查看Xcode控制台
   - 查找相关的日志信息

## 备用方案

如果以上方法都不行，可以：

1. **将mock.json移动到项目根目录**
2. **或者使用内嵌的示例数据**（当前代码已经包含了这个备用方案）

## 验证成功

当文件正确添加后，您应该看到：
- ✅ 轮播图显示真实的电影数据
- ✅ 控制台输出 "成功从Bundle加载mock.json"
- ✅ 多个轮播图项目（而不是重复的示例数据）
