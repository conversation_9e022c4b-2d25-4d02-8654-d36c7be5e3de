{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "6NQ3S69HUU", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "044fc009e77643358a7e0e8eafeec5d4c96e17323dd9dd9286a8820894b47fd2", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "6NQ3S69HUU", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "044fc009e77643358a7e0e8eafeec5d4039e3c949cd8cdb78c581493f136faf1", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"children": [{"fileType": "text.json", "guid": "044fc009e77643358a7e0e8eafeec5d4d06ae9220f96d9c898ce949da6d3ba28", "path": "mock.json", "sourceTree": "<group>", "type": "file"}], "guid": "044fc009e77643358a7e0e8eafeec5d455ee5ee6fb07030e7c6cd758ea26eb1c", "name": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d4c04355b9912d0c12e9b18f6e00c0c60a", "path": "MockDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "044fc009e77643358a7e0e8eafeec5d4e555f3d6d6ba4236e46f7802c148aad5", "name": "Data", "path": "Data", "sourceTree": "<group>", "type": "group"}, {"guid": "044fc009e77643358a7e0e8eafeec5d474ba22033a3cce7904f3121f24af3607", "name": "Design", "path": "Design", "sourceTree": "<group>", "type": "group"}, {"guid": "044fc009e77643358a7e0e8eafeec5d4cdd7c9828eed4b0e08e317e9fe49d37f", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d4061ff55920fcb987c64c24a1cd82d7f8", "path": "Movie.swift", "sourceTree": "<group>", "type": "file"}], "guid": "044fc009e77643358a7e0e8eafeec5d4f57bbdb31ff26e8a639d799caca20e1b", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d452f8fea53bda18afd29a31a33b627fee", "path": "ImageColorExtractor.swift", "sourceTree": "<group>", "type": "file"}], "guid": "044fc009e77643358a7e0e8eafeec5d4532fd7f87f9005ef967be96cebd24751", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d432144adcb17c7e86726f1dc2700f452c", "path": "BottomTabBarView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d4ff08c3a767974adaed3e3000c2ec8d25", "path": "ContentSectionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d4da1415d25aee7037728252cda23c2136", "path": "HeroCarouselView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "044fc009e77643358a7e0e8eafeec5d4903fb99dbcf3843ea9594763893f8d9e", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d43c685a5dce024963cb1f6e4daadd9999", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "044fc009e77643358a7e0e8eafeec5d4c27666602e57d18c6bbb462b953a9550", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "044fc009e77643358a7e0e8eafeec5d417ffbf8fe1e4b0acb78576a7f9a46c27", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d461909deab20276147dcba9b8f99fbb49", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d4fc87dc382ec73d2b98c808e073707030", "path": "PrismApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "044fc009e77643358a7e0e8eafeec5d4ae90f410be2889ea5609045c3788dfb2", "name": "Prism", "path": "Prism", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d44818f8652a268532e9f7ab80feb9dddb", "path": "PrismTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "044fc009e77643358a7e0e8eafeec5d4ed69ab6595b4ed1118cd578bb7033894", "name": "PrismTests", "path": "PrismTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d40557aa9b6c453777fc746413d0beaffc", "path": "PrismUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "044fc009e77643358a7e0e8eafeec5d4ff414a46773a34c83f43709f49c04287", "path": "PrismUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "044fc009e77643358a7e0e8eafeec5d41af2ec1fcecd5bf2f99e53c3227fe8fc", "name": "PrismUITests", "path": "PrismUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "044fc009e77643358a7e0e8eafeec5d448ce222049fe2d1e437d0f8fc5ffdcd1", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "044fc009e77643358a7e0e8eafeec5d478561d6d5d99a21f9ecd768f8c415613", "name": "Prism", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "044fc009e77643358a7e0e8eafeec5d4", "path": "/Users/<USER>/project/Prism/Prism/Prism.xcodeproj", "projectDirectory": "/Users/<USER>/project/Prism/Prism", "targets": ["TARGET@v11_hash=780e40de888161593ff05712f029bb95", "TARGET@v11_hash=bb7b405e9fda0d779f55c1ad4c511f11", "TARGET@v11_hash=b7ae273523b9a506a27bd81e8407ed42"]}