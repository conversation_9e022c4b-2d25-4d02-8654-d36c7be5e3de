{"buildConfigurations": [{"buildSettings": {"CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "6NQ3S69HUU", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "Prism.PrismUITests", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "TEST_TARGET_NAME": "Prism"}, "guid": "044fc009e77643358a7e0e8eafeec5d44ebb99beb939ce858bb3f20bb7f7a5ed", "name": "Debug"}, {"buildSettings": {"CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "6NQ3S69HUU", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "Prism.PrismUITests", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "TEST_TARGET_NAME": "Prism"}, "guid": "044fc009e77643358a7e0e8eafeec5d430f07e7db7b738515135e59ddd9f20d7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "044fc009e77643358a7e0e8eafeec5d40557aa9b6c453777fc746413d0beaffc", "guid": "044fc009e77643358a7e0e8eafeec5d4f14e08e4e849d9e9fce8761cb821dea0"}, {"fileReference": "044fc009e77643358a7e0e8eafeec5d4ff414a46773a34c83f43709f49c04287", "guid": "044fc009e77643358a7e0e8eafeec5d47f387584b1ab50512e9614920ecddb9b"}], "guid": "044fc009e77643358a7e0e8eafeec5d4d98cb3aae293c441bb4ad8e447db32ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "044fc009e77643358a7e0e8eafeec5d414684f332ddedad64e70e58398c4b01a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "044fc009e77643358a7e0e8eafeec5d4fa7eb655a4bdee6cc521b2616161b782", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "044fc009e77643358a7e0e8eafeec5d43eac621689c99293a9467d1e907ddd4e", "name": "Prism"}], "guid": "044fc009e77643358a7e0e8eafeec5d4fc6d0c23db7e043954ce5ed7b3fa2fee", "name": "PrismUITests", "performanceTestsBaselinesPath": "/Users/<USER>/project/Prism/Prism/Prism.xcodeproj/xcshareddata/xcbaselines/6FB4D2F02E38DB440039902F.xcbaseline", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "044fc009e77643358a7e0e8eafeec5d4e219d02c175a37642cf9a43442d607c3", "name": "PrismUITests.xctest", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle.ui-testing", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}