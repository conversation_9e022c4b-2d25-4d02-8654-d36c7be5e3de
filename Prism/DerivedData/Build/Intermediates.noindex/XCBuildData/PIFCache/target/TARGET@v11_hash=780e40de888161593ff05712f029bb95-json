{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "6NQ3S69HUU", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "Prism.Prism", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "044fc009e77643358a7e0e8eafeec5d4a7f856646d98c99856dbdaf7b52b63b5", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "6NQ3S69HUU", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "Prism.Prism", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "044fc009e77643358a7e0e8eafeec5d4157e8f22344136b9a326984b65f78592", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "044fc009e77643358a7e0e8eafeec5d4061ff55920fcb987c64c24a1cd82d7f8", "guid": "044fc009e77643358a7e0e8eafeec5d405b2ce431b49e515e4491613fc2ba621"}, {"fileReference": "044fc009e77643358a7e0e8eafeec5d4c04355b9912d0c12e9b18f6e00c0c60a", "guid": "044fc009e77643358a7e0e8eafeec5d474e6c4294fb2d7aacf36cd457c10a731"}, {"fileReference": "044fc009e77643358a7e0e8eafeec5d4fc87dc382ec73d2b98c808e073707030", "guid": "044fc009e77643358a7e0e8eafeec5d4b92fb3c7981fcd7c9d56e293dd1ddea0"}, {"fileReference": "044fc009e77643358a7e0e8eafeec5d4da1415d25aee7037728252cda23c2136", "guid": "044fc009e77643358a7e0e8eafeec5d419075b3296b58b7c4a7e0208f1dfedd7"}, {"fileReference": "044fc009e77643358a7e0e8eafeec5d461909deab20276147dcba9b8f99fbb49", "guid": "044fc009e77643358a7e0e8eafeec5d469bfb9a01d161ed6eabb54b147ef1689"}, {"fileReference": "044fc009e77643358a7e0e8eafeec5d4ff08c3a767974adaed3e3000c2ec8d25", "guid": "044fc009e77643358a7e0e8eafeec5d4b1e4f1e201a802daf2831c7df70da7d2"}, {"fileReference": "044fc009e77643358a7e0e8eafeec5d452f8fea53bda18afd29a31a33b627fee", "guid": "044fc009e77643358a7e0e8eafeec5d4a4a2dd9ddb1e32a491ca787f22cc40b0"}, {"fileReference": "044fc009e77643358a7e0e8eafeec5d432144adcb17c7e86726f1dc2700f452c", "guid": "044fc009e77643358a7e0e8eafeec5d4940f641101e3fbe318ea53a769e19e17"}, {"fileReference": "044fc009e77643358a7e0e8eafeec5d43c685a5dce024963cb1f6e4daadd9999", "guid": "044fc009e77643358a7e0e8eafeec5d490bd6fc829a5257a079fd78d31c6cc15"}], "guid": "044fc009e77643358a7e0e8eafeec5d45deaceea24d97c522579075cabf3fe67", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "044fc009e77643358a7e0e8eafeec5d4cb3640e683ca887da4bce31bf12e87d0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "044fc009e77643358a7e0e8eafeec5d4d06ae9220f96d9c898ce949da6d3ba28", "guid": "044fc009e77643358a7e0e8eafeec5d4dbf32b06f8ac89ec7558b4381efa0f19"}, {"fileReference": "044fc009e77643358a7e0e8eafeec5d417ffbf8fe1e4b0acb78576a7f9a46c27", "guid": "044fc009e77643358a7e0e8eafeec5d48fc541ae8c1cd54ce7f3ec669b3e220b"}], "guid": "044fc009e77643358a7e0e8eafeec5d48a484a7093dc144d9968c9faccf2957b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "044fc009e77643358a7e0e8eafeec5d43eac621689c99293a9467d1e907ddd4e", "name": "Prism", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "044fc009e77643358a7e0e8eafeec5d4180e7b601faf0cda36c489a83a046c0d", "name": "Prism.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}