{"buildConfigurations": [{"buildSettings": {"BUNDLE_LOADER": "$(TEST_HOST)", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "6NQ3S69HUU", "GENERATE_INFOPLIST_FILE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "Prism.PrismTests", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "TEST_HOST": "$(BUILT_PRODUCTS_DIR)/Prism.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Prism"}, "guid": "044fc009e77643358a7e0e8eafeec5d4abd50ae0b99ed5d0bec37600d67e86f3", "name": "Debug"}, {"buildSettings": {"BUNDLE_LOADER": "$(TEST_HOST)", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "6NQ3S69HUU", "GENERATE_INFOPLIST_FILE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "Prism.PrismTests", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "TEST_HOST": "$(BUILT_PRODUCTS_DIR)/Prism.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Prism"}, "guid": "044fc009e77643358a7e0e8eafeec5d49018686303c09cee9d7600557de47cb1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "044fc009e77643358a7e0e8eafeec5d44818f8652a268532e9f7ab80feb9dddb", "guid": "044fc009e77643358a7e0e8eafeec5d4c5148ae553168f4854541f984b9d3582"}], "guid": "044fc009e77643358a7e0e8eafeec5d4003cfc0a1654cb8803f9df6c7ba97e5d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "044fc009e77643358a7e0e8eafeec5d4ca58b1f59da975a3960c12e0464acf3e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "044fc009e77643358a7e0e8eafeec5d404e78ed81643b92957d36350edfffa45", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "044fc009e77643358a7e0e8eafeec5d43eac621689c99293a9467d1e907ddd4e", "name": "Prism"}], "guid": "044fc009e77643358a7e0e8eafeec5d4c27f608ed3cfe8a539237accbcdc5d45", "name": "PrismTests", "performanceTestsBaselinesPath": "/Users/<USER>/project/Prism/Prism/Prism.xcodeproj/xcshareddata/xcbaselines/6FB4D2E62E38DB440039902F.xcbaseline", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "044fc009e77643358a7e0e8eafeec5d4d03ebd7f99acbd3340e6d68d996252d9", "name": "PrismTests.xctest", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle.unit-test", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}