//
//  TMDBService.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import Foundation

// MARK: - TMDB API 配置
struct TMDBConfig {
    static let apiKey = "ab32cd4b31abae06d59967edaefa99ee"
    static let accessToken = "eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJhYjMyY2Q0YjMxYWJhZTA2ZDU5OTY3ZWRhZWZhOTllZSIsIm5iZiI6MTcwMDEyODgzMC43NjEsInN1YiI6IjY1NTVlODNlMDgxNmM3MDExYTBjMjY1ZSIsInNjb3BlcyI6WyJhcGlfcmVhZCJdLCJ2ZXJzaW9uIjoxfQ.xsEJHUrtf3qpWIBAyPhZnn-BI3K6qn5fhQNlr8H4jhY"
    static let baseURL = "https://api.themoviedb.org/3"
    static let imageBaseURL = "https://image.tmdb.org/t/p"
}

// MARK: - TMDB 电影数据模型
struct TMDBMovie: Codable, Identifiable {
    let id: Int
    let title: String
    let originalTitle: String
    let overview: String
    let posterPath: String?
    let backdropPath: String?
    let releaseDate: String
    let voteAverage: Double
    let voteCount: Int
    let popularity: Double
    let genreIds: [Int]
    let adult: Bool
    let originalLanguage: String
    let video: Bool
    
    private enum CodingKeys: String, CodingKey {
        case id
        case title
        case originalTitle = "original_title"
        case overview
        case posterPath = "poster_path"
        case backdropPath = "backdrop_path"
        case releaseDate = "release_date"
        case voteAverage = "vote_average"
        case voteCount = "vote_count"
        case popularity
        case genreIds = "genre_ids"
        case adult
        case originalLanguage = "original_language"
        case video
    }
    
    // 计算属性：完整的海报URL
    var fullPosterURL: String {
        guard let posterPath = posterPath else { return "" }
        return "\(TMDBConfig.imageBaseURL)/w500\(posterPath)"
    }
    
    // 计算属性：完整的背景图URL
    var fullBackdropURL: String {
        guard let backdropPath = backdropPath else { return fullPosterURL }
        return "\(TMDBConfig.imageBaseURL)/w1280\(backdropPath)"
    }
    
    // 计算属性：高清海报URL
    var hdPosterURL: String {
        guard let posterPath = posterPath else { return "" }
        return "\(TMDBConfig.imageBaseURL)/w780\(posterPath)"
    }
}

// MARK: - TMDB API 响应模型
struct TMDBMovieResponse: Codable {
    let page: Int
    let results: [TMDBMovie]
    let totalPages: Int
    let totalResults: Int
    
    private enum CodingKeys: String, CodingKey {
        case page
        case results
        case totalPages = "total_pages"
        case totalResults = "total_results"
    }
}

// MARK: - TMDB 电影详情模型
struct TMDBMovieDetails: Codable {
    let id: Int
    let title: String
    let originalTitle: String
    let overview: String
    let posterPath: String?
    let backdropPath: String?
    let releaseDate: String
    let runtime: Int?
    let voteAverage: Double
    let voteCount: Int
    let popularity: Double
    let genres: [TMDBGenre]
    let productionCompanies: [TMDBProductionCompany]
    let spokenLanguages: [TMDBSpokenLanguage]
    let status: String
    let tagline: String?
    
    private enum CodingKeys: String, CodingKey {
        case id, title, overview, runtime, popularity, genres, status, tagline
        case originalTitle = "original_title"
        case posterPath = "poster_path"
        case backdropPath = "backdrop_path"
        case releaseDate = "release_date"
        case voteAverage = "vote_average"
        case voteCount = "vote_count"
        case productionCompanies = "production_companies"
        case spokenLanguages = "spoken_languages"
    }
}

// MARK: - TMDB 类型模型
struct TMDBGenre: Codable {
    let id: Int
    let name: String
}

struct TMDBProductionCompany: Codable {
    let id: Int
    let name: String
    let logoPath: String?
    let originCountry: String
    
    private enum CodingKeys: String, CodingKey {
        case id, name
        case logoPath = "logo_path"
        case originCountry = "origin_country"
    }
}

struct TMDBSpokenLanguage: Codable {
    let englishName: String
    let iso6391: String
    let name: String
    
    private enum CodingKeys: String, CodingKey {
        case englishName = "english_name"
        case iso6391 = "iso_639_1"
        case name
    }
}

// MARK: - API 端点枚举
enum TMDBEndpoint {
    case popular(page: Int = 1)
    case nowPlaying(page: Int = 1)
    case topRated(page: Int = 1)
    case upcoming(page: Int = 1)
    case movieDetails(id: Int)
    case search(query: String, page: Int = 1)
    
    var path: String {
        switch self {
        case .popular(let page):
            return "/movie/popular?page=\(page)"
        case .nowPlaying(let page):
            return "/movie/now_playing?page=\(page)"
        case .topRated(let page):
            return "/movie/top_rated?page=\(page)"
        case .upcoming(let page):
            return "/movie/upcoming?page=\(page)"
        case .movieDetails(let id):
            return "/movie/\(id)"
        case .search(let query, let page):
            let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
            return "/search/movie?query=\(encodedQuery)&page=\(page)"
        }
    }
    
    var url: URL? {
        return URL(string: TMDBConfig.baseURL + path)
    }
}

// MARK: - TMDB API 服务类
class TMDBService: ObservableObject {
    static let shared = TMDBService()
    
    private init() {}
    
    // 通用网络请求方法
    private func performRequest<T: Codable>(
        endpoint: TMDBEndpoint,
        responseType: T.Type
    ) async throws -> T {
        guard let url = endpoint.url else {
            throw TMDBError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("Bearer \(TMDBConfig.accessToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw TMDBError.invalidResponse
        }
        
        guard 200...299 ~= httpResponse.statusCode else {
            throw TMDBError.serverError(httpResponse.statusCode)
        }
        
        do {
            let decodedResponse = try JSONDecoder().decode(responseType, from: data)
            return decodedResponse
        } catch {
            throw TMDBError.decodingError(error)
        }
    }
    
    // 获取热门电影
    func fetchPopularMovies(page: Int = 1) async throws -> TMDBMovieResponse {
        return try await performRequest(
            endpoint: .popular(page: page),
            responseType: TMDBMovieResponse.self
        )
    }
    
    // 获取正在上映的电影
    func fetchNowPlayingMovies(page: Int = 1) async throws -> TMDBMovieResponse {
        return try await performRequest(
            endpoint: .nowPlaying(page: page),
            responseType: TMDBMovieResponse.self
        )
    }
    
    // 获取高评分电影
    func fetchTopRatedMovies(page: Int = 1) async throws -> TMDBMovieResponse {
        return try await performRequest(
            endpoint: .topRated(page: page),
            responseType: TMDBMovieResponse.self
        )
    }
    
    // 获取即将上映的电影
    func fetchUpcomingMovies(page: Int = 1) async throws -> TMDBMovieResponse {
        return try await performRequest(
            endpoint: .upcoming(page: page),
            responseType: TMDBMovieResponse.self
        )
    }
    
    // 获取电影详情
    func fetchMovieDetails(id: Int) async throws -> TMDBMovieDetails {
        return try await performRequest(
            endpoint: .movieDetails(id: id),
            responseType: TMDBMovieDetails.self
        )
    }
    
    // 搜索电影
    func searchMovies(query: String, page: Int = 1) async throws -> TMDBMovieResponse {
        return try await performRequest(
            endpoint: .search(query: query, page: page),
            responseType: TMDBMovieResponse.self
        )
    }
}

// MARK: - 错误处理
enum TMDBError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case serverError(Int)
    case decodingError(Error)
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .invalidResponse:
            return "无效的响应"
        case .serverError(let code):
            return "服务器错误: \(code)"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        }
    }
}
