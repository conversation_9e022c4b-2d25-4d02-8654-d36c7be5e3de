//
//  TMDBDataConverter.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import Foundation

// MARK: - TMDB 数据转换器
struct TMDBDataConverter {
    
    // MARK: - 类型映射
    private static let genreMapping: [Int: String] = [
        28: "动作",
        12: "冒险",
        16: "动画",
        35: "喜剧",
        80: "犯罪",
        99: "纪录片",
        18: "剧情",
        10751: "家庭",
        14: "奇幻",
        36: "历史",
        27: "恐怖",
        10402: "音乐",
        9648: "悬疑",
        10749: "爱情",
        878: "科幻",
        10770: "电视电影",
        53: "惊悚",
        10752: "战争",
        37: "西部"
    ]
    
    // MARK: - 转换 TMDBMovie 到 MediaItem
    static func convertToMediaItem(_ tmdbMovie: TMDBMovie) -> MediaItem {
        // 转换类型ID为中文名称
        let categories = tmdbMovie.genreIds.compactMap { genreMapping[$0] }.joined(separator: ",")
        
        // 格式化评分
        let score = String(format: "%.1f", tmdbMovie.voteAverage)
        
        // 生成演员信息（TMDB基础API不包含演员信息，这里使用占位符）
        let actors = generateActorPlaceholder()
        
        // 生成描述信息
        let description = tmdbMovie.overview.isEmpty ? "暂无简介" : tmdbMovie.overview
        
        // 生成标签
        let tag = generateTags(for: tmdbMovie)
        
        // 生成备注信息
        let remarks = generateRemarks(for: tmdbMovie)
        
        return MediaItem(
            id: tmdbMovie.id,
            name: tmdbMovie.title,
            englishName: tmdbMovie.originalTitle,
            level: 0, // TMDB没有level概念，设为0
            posterURL: tmdbMovie.fullPosterURL,
            thumbnailURL: tmdbMovie.hdPosterURL,
            tag: tag,
            category: categories.isEmpty ? "电影" : categories,
            remarks: remarks,
            serial: "", // TMDB没有serial概念
            score: score,
            doubanScore: "", // TMDB没有豆瓣评分
            subtitle: tmdbMovie.originalLanguage.uppercased(),
            actors: actors,
            description: description
        )
    }
    
    // MARK: - 转换 TMDBMovieDetails 到 MediaItem
    static func convertToMediaItem(_ tmdbDetails: TMDBMovieDetails) -> MediaItem {
        // 转换类型为中文名称
        let categories = tmdbDetails.genres.map { $0.name }.joined(separator: ",")
        
        // 格式化评分
        let score = String(format: "%.1f", tmdbDetails.voteAverage)
        
        // 生成演员信息（需要额外API调用获取，这里使用占位符）
        let actors = generateActorPlaceholder()
        
        // 生成描述信息
        let description = tmdbDetails.overview.isEmpty ? "暂无简介" : tmdbDetails.overview
        
        // 生成标签
        let tag = generateTagsFromDetails(tmdbDetails)
        
        // 生成备注信息
        let remarks = generateRemarksFromDetails(tmdbDetails)
        
        // 生成海报URL
        let posterURL = tmdbDetails.posterPath != nil ? 
            "\(TMDBConfig.imageBaseURL)/w500\(tmdbDetails.posterPath!)" : ""
        let thumbnailURL = tmdbDetails.posterPath != nil ? 
            "\(TMDBConfig.imageBaseURL)/w780\(tmdbDetails.posterPath!)" : ""
        
        return MediaItem(
            id: tmdbDetails.id,
            name: tmdbDetails.title,
            englishName: tmdbDetails.originalTitle,
            level: 0,
            posterURL: posterURL,
            thumbnailURL: thumbnailURL,
            tag: tag,
            category: categories.isEmpty ? "电影" : categories,
            remarks: remarks,
            serial: "",
            score: score,
            doubanScore: "",
            subtitle: tmdbDetails.spokenLanguages.first?.name ?? tmdbDetails.originalTitle,
            actors: actors,
            description: description
        )
    }
    
    // MARK: - 批量转换
    static func convertToMediaItems(_ tmdbMovies: [TMDBMovie]) -> [MediaItem] {
        return tmdbMovies.map { convertToMediaItem($0) }
    }
    
    static func convertToCarouselItems(_ tmdbMovies: [TMDBMovie]) -> [CarouselItem] {
        return tmdbMovies.map { tmdbMovie in
            let mediaItem = convertToMediaItem(tmdbMovie)
            return CarouselItem(
                mediaItem: mediaItem,
                releaseDate: formatReleaseDate(tmdbMovie.releaseDate)
            )
        }
    }
    
    // MARK: - 辅助方法
    
    // 生成演员占位符
    private static func generateActorPlaceholder() -> String {
        let placeholderActors = [
            "知名演员", "实力派演员", "人气演员", "资深演员", "新生代演员"
        ]
        return placeholderActors.randomElement() ?? "演员信息待更新"
    }
    
    // 生成标签
    private static func generateTags(for movie: TMDBMovie) -> String {
        var tags: [String] = []
        
        // 根据评分生成标签
        if movie.voteAverage >= 8.0 {
            tags.append("高分佳作")
        } else if movie.voteAverage >= 7.0 {
            tags.append("口碑不错")
        }
        
        // 根据受欢迎程度生成标签
        if movie.popularity > 100 {
            tags.append("热门")
        }
        
        // 根据投票数生成标签
        if movie.voteCount > 1000 {
            tags.append("观众推荐")
        }
        
        // 根据发布日期生成标签
        if isRecentRelease(movie.releaseDate) {
            tags.append("新片")
        }
        
        return tags.isEmpty ? "精选推荐" : tags.joined(separator: ",")
    }
    
    // 从详情生成标签
    private static func generateTagsFromDetails(_ details: TMDBMovieDetails) -> String {
        var tags: [String] = []
        
        if details.voteAverage >= 8.0 {
            tags.append("高分佳作")
        }
        
        if let runtime = details.runtime {
            if runtime > 150 {
                tags.append("史诗巨制")
            } else if runtime < 90 {
                tags.append("精简佳作")
            }
        }
        
        if details.popularity > 100 {
            tags.append("热门")
        }
        
        return tags.isEmpty ? "精选推荐" : tags.joined(separator: ",")
    }
    
    // 生成备注信息
    private static func generateRemarks(for movie: TMDBMovie) -> String {
        if movie.voteAverage >= 8.0 {
            return "强烈推荐"
        } else if movie.voteAverage >= 7.0 {
            return "值得一看"
        } else if movie.voteAverage >= 6.0 {
            return "还不错"
        } else {
            return "普通"
        }
    }
    
    // 从详情生成备注
    private static func generateRemarksFromDetails(_ details: TMDBMovieDetails) -> String {
        var remarks: [String] = []
        
        if let runtime = details.runtime {
            remarks.append("\(runtime)分钟")
        }
        
        if details.voteAverage >= 8.0 {
            remarks.append("强烈推荐")
        } else if details.voteAverage >= 7.0 {
            remarks.append("值得一看")
        }
        
        return remarks.isEmpty ? "精选影片" : remarks.joined(separator: " · ")
    }
    
    // 格式化发布日期
    private static func formatReleaseDate(_ dateString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        
        if let date = formatter.date(from: dateString) {
            formatter.dateFormat = "yyyy年MM月dd日"
            return formatter.string(from: date)
        }
        
        return dateString
    }
    
    // 检查是否为近期发布
    private static func isRecentRelease(_ dateString: String) -> Bool {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        
        guard let releaseDate = formatter.date(from: dateString) else {
            return false
        }
        
        let now = Date()
        let threeMonthsAgo = Calendar.current.date(byAdding: .month, value: -3, to: now) ?? now
        
        return releaseDate >= threeMonthsAgo
    }
    
    // MARK: - 图片URL辅助方法
    
    // 获取不同尺寸的海报URL
    static func getPosterURL(_ posterPath: String?, size: PosterSize = .medium) -> String {
        guard let posterPath = posterPath else { return "" }
        return "\(TMDBConfig.imageBaseURL)/\(size.rawValue)\(posterPath)"
    }
    
    // 获取背景图URL
    static func getBackdropURL(_ backdropPath: String?, size: BackdropSize = .large) -> String {
        guard let backdropPath = backdropPath else { return "" }
        return "\(TMDBConfig.imageBaseURL)/\(size.rawValue)\(backdropPath)"
    }
}

// MARK: - 图片尺寸枚举
enum PosterSize: String {
    case small = "w185"
    case medium = "w342"
    case large = "w500"
    case xlarge = "w780"
    case original = "original"
}

enum BackdropSize: String {
    case small = "w300"
    case medium = "w780"
    case large = "w1280"
    case original = "original"
}
