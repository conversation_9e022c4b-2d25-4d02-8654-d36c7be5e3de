//
//  TMDBDataManager.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import Foundation
import SwiftUI

// MARK: - TMDB 数据管理器
@MainActor
class TMDBDataManager: ObservableObject {
    // MARK: - Published Properties
    @Published var featuredItems: [CarouselItem] = []
    @Published var todayTrending: [MediaItem] = []
    @Published var weekTrending: [MediaItem] = []
    @Published var popularMovies: [MediaItem] = []
    @Published var topRatedMovies: [MediaItem] = []
    @Published var upcomingMovies: [MediaItem] = []
    @Published var nowPlayingMovies: [MediaItem] = []
    
    // MARK: - Loading States
    @Published var isLoading = false
    @Published var isLoadingFeatured = false
    @Published var isLoadingTrending = false
    @Published var isLoadingPopular = false
    @Published var isLoadingTopRated = false
    @Published var isLoadingUpcoming = false
    @Published var isLoadingNowPlaying = false
    
    // MARK: - Error Handling
    @Published var errorMessage: String?
    @Published var hasError = false
    
    // MARK: - Services
    private let tmdbService = TMDBService.shared
    
    // MARK: - Initialization
    init() {
        Task {
            await loadAllData()
        }
    }
    
    // MARK: - 加载所有数据
    func loadAllData() async {
        isLoading = true
        
        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.loadFeaturedMovies() }
            group.addTask { await self.loadTrendingMovies() }
            group.addTask { await self.loadPopularMovies() }
            group.addTask { await self.loadTopRatedMovies() }
            group.addTask { await self.loadUpcomingMovies() }
            group.addTask { await self.loadNowPlayingMovies() }
        }
        
        isLoading = false
    }
    
    // MARK: - 加载轮播图数据（使用热门电影）
    func loadFeaturedMovies() async {
        isLoadingFeatured = true
        
        do {
            let response = try await tmdbService.fetchPopularMovies(page: 1)
            let featuredMovies = Array(response.results.prefix(5)) // 取前5个作为轮播图
            featuredItems = TMDBDataConverter.convertToCarouselItems(featuredMovies)
        } catch {
            handleError(error, context: "加载轮播图数据")
        }
        
        isLoadingFeatured = false
    }
    
    // MARK: - 加载趋势数据
    func loadTrendingMovies() async {
        isLoadingTrending = true
        
        do {
            // 使用正在上映的电影作为今日趋势
            let nowPlayingResponse = try await tmdbService.fetchNowPlayingMovies(page: 1)
            todayTrending = Array(TMDBDataConverter.convertToMediaItems(nowPlayingResponse.results).prefix(10))
            
            // 使用高评分电影作为本周趋势
            let topRatedResponse = try await tmdbService.fetchTopRatedMovies(page: 1)
            weekTrending = Array(TMDBDataConverter.convertToMediaItems(topRatedResponse.results).prefix(10))
            
        } catch {
            handleError(error, context: "加载趋势数据")
        }
        
        isLoadingTrending = false
    }
    
    // MARK: - 加载热门电影
    func loadPopularMovies() async {
        isLoadingPopular = true
        
        do {
            let response = try await tmdbService.fetchPopularMovies(page: 1)
            popularMovies = TMDBDataConverter.convertToMediaItems(response.results)
        } catch {
            handleError(error, context: "加载热门电影")
        }
        
        isLoadingPopular = false
    }
    
    // MARK: - 加载高评分电影
    func loadTopRatedMovies() async {
        isLoadingTopRated = true
        
        do {
            let response = try await tmdbService.fetchTopRatedMovies(page: 1)
            topRatedMovies = TMDBDataConverter.convertToMediaItems(response.results)
        } catch {
            handleError(error, context: "加载高评分电影")
        }
        
        isLoadingTopRated = false
    }
    
    // MARK: - 加载即将上映电影
    func loadUpcomingMovies() async {
        isLoadingUpcoming = true
        
        do {
            let response = try await tmdbService.fetchUpcomingMovies(page: 1)
            upcomingMovies = TMDBDataConverter.convertToMediaItems(response.results)
        } catch {
            handleError(error, context: "加载即将上映电影")
        }
        
        isLoadingUpcoming = false
    }
    
    // MARK: - 加载正在上映电影
    func loadNowPlayingMovies() async {
        isLoadingNowPlaying = true
        
        do {
            let response = try await tmdbService.fetchNowPlayingMovies(page: 1)
            nowPlayingMovies = TMDBDataConverter.convertToMediaItems(response.results)
        } catch {
            handleError(error, context: "加载正在上映电影")
        }
        
        isLoadingNowPlaying = false
    }
    
    // MARK: - 搜索电影
    func searchMovies(query: String) async -> [MediaItem] {
        guard !query.trimmingCharacters(in: .whitespaces).isEmpty else {
            return []
        }
        
        do {
            let response = try await tmdbService.searchMovies(query: query)
            return TMDBDataConverter.convertToMediaItems(response.results)
        } catch {
            handleError(error, context: "搜索电影")
            return []
        }
    }
    
    // MARK: - 获取电影详情
    func getMovieDetails(id: Int) async -> MediaItem? {
        do {
            let details = try await tmdbService.fetchMovieDetails(id: id)
            return TMDBDataConverter.convertToMediaItem(details)
        } catch {
            handleError(error, context: "获取电影详情")
            return nil
        }
    }
    
    // MARK: - 刷新数据
    func refreshAllData() async {
        await loadAllData()
    }
    
    func refreshFeaturedMovies() async {
        await loadFeaturedMovies()
    }
    
    func refreshTrendingMovies() async {
        await loadTrendingMovies()
    }
    
    // MARK: - 错误处理
    private func handleError(_ error: Error, context: String) {
        let errorMsg = "\(context)失败: \(error.localizedDescription)"
        print("❌ \(errorMsg)")
        
        errorMessage = errorMsg
        hasError = true
        
        // 3秒后自动清除错误状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.hasError = false
            self.errorMessage = nil
        }
    }
    
    // MARK: - 清除错误
    func clearError() {
        hasError = false
        errorMessage = nil
    }
    
    // MARK: - 数据状态检查
    var hasData: Bool {
        !featuredItems.isEmpty || !todayTrending.isEmpty || !weekTrending.isEmpty
    }
    
    var isAnyLoading: Bool {
        isLoading || isLoadingFeatured || isLoadingTrending || 
        isLoadingPopular || isLoadingTopRated || isLoadingUpcoming || isLoadingNowPlaying
    }
}

// MARK: - 便利方法扩展
extension TMDBDataManager {
    
    // 获取内容分区
    func getContentSections() -> [ContentSection] {
        var sections: [ContentSection] = []
        
        if !todayTrending.isEmpty {
            sections.append(ContentSection(
                title: "今日热门",
                items: todayTrending,
                sectionType: .todayTrending
            ))
        }
        
        if !weekTrending.isEmpty {
            sections.append(ContentSection(
                title: "本周推荐",
                items: weekTrending,
                sectionType: .weekTrending
            ))
        }
        
        if !popularMovies.isEmpty {
            sections.append(ContentSection(
                title: "热门电影",
                items: popularMovies,
                sectionType: .recommended
            ))
        }
        
        if !upcomingMovies.isEmpty {
            sections.append(ContentSection(
                title: "即将上映",
                items: upcomingMovies,
                sectionType: .newReleases
            ))
        }
        
        return sections
    }
    
    // 获取随机推荐
    func getRandomRecommendations(count: Int = 10) -> [MediaItem] {
        let allMovies = popularMovies + topRatedMovies + nowPlayingMovies
        return Array(allMovies.shuffled().prefix(count))
    }
    
    // 根据类型获取电影
    func getMoviesByGenre(_ genre: String) -> [MediaItem] {
        let allMovies = popularMovies + topRatedMovies + nowPlayingMovies + upcomingMovies
        return allMovies.filter { $0.categoryList.contains(genre) }
    }
}
