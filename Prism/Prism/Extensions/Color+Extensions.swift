//
//  Color+Extensions.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import SwiftUI
import UIKit

// MARK: - Color 扩展
extension Color {
    
    // MARK: - 十六进制颜色初始化
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    // MARK: - 颜色亮度调整
    func lighter(by percentage: Double = 0.2) -> Color {
        return self.adjustBrightness(by: percentage)
    }
    
    func darker(by percentage: Double = 0.2) -> Color {
        return self.adjustBrightness(by: -percentage)
    }
    
    private func adjustBrightness(by percentage: Double) -> Color {
        let uiColor = UIColor(self)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        return Color(
            red: max(0, min(1, red + percentage)),
            green: max(0, min(1, green + percentage)),
            blue: max(0, min(1, blue + percentage)),
            opacity: alpha
        )
    }
    
    // MARK: - 颜色饱和度调整
    func saturated(by percentage: Double = 0.2) -> Color {
        return self.adjustSaturation(by: percentage)
    }
    
    func desaturated(by percentage: Double = 0.2) -> Color {
        return self.adjustSaturation(by: -percentage)
    }
    
    private func adjustSaturation(by percentage: Double) -> Color {
        let uiColor = UIColor(self)
        var hue: CGFloat = 0
        var saturation: CGFloat = 0
        var brightness: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha)
        
        return Color(
            hue: hue,
            saturation: max(0, min(1, saturation + percentage)),
            brightness: brightness,
            opacity: alpha
        )
    }
    
    // MARK: - 颜色混合
    func blended(with color: Color, ratio: Double = 0.5) -> Color {
        let selfUIColor = UIColor(self)
        let otherUIColor = UIColor(color)
        
        var r1: CGFloat = 0, g1: CGFloat = 0, b1: CGFloat = 0, a1: CGFloat = 0
        var r2: CGFloat = 0, g2: CGFloat = 0, b2: CGFloat = 0, a2: CGFloat = 0
        
        selfUIColor.getRed(&r1, green: &g1, blue: &b1, alpha: &a1)
        otherUIColor.getRed(&r2, green: &g2, blue: &b2, alpha: &a2)
        
        let ratio = max(0, min(1, ratio))
        
        return Color(
            red: r1 * (1 - ratio) + r2 * ratio,
            green: g1 * (1 - ratio) + g2 * ratio,
            blue: b1 * (1 - ratio) + b2 * ratio,
            opacity: a1 * (1 - ratio) + a2 * ratio
        )
    }
    
    // MARK: - 颜色属性获取
    var brightness: Double {
        let uiColor = UIColor(self)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        return (red * 0.299 + green * 0.587 + blue * 0.114)
    }
    
    var isLight: Bool {
        return brightness > 0.5
    }
    
    var isDark: Bool {
        return brightness <= 0.5
    }
    
    // MARK: - 对比色
    var contrastingColor: Color {
        return isLight ? .black : .white
    }
    
    // MARK: - 十六进制字符串
    var hexString: String {
        let uiColor = UIColor(self)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        let r = Int(red * 255)
        let g = Int(green * 255)
        let b = Int(blue * 255)
        
        return String(format: "#%02X%02X%02X", r, g, b)
    }
    
    // MARK: - 预定义颜色主题
    static let movieGenreColors = [
        "action": Color.red,
        "adventure": Color.orange,
        "comedy": Color.yellow,
        "drama": Color.purple,
        "horror": Color.black,
        "romance": Color.pink,
        "sci-fi": Color.blue,
        "thriller": Color.gray,
        "fantasy": Color.green,
        "mystery": Color.indigo
    ]
    
    static func forGenre(_ genre: String) -> Color {
        return movieGenreColors[genre.lowercased()] ?? .primary
    }
    
    // MARK: - 渐变色组合
    static func createGradient(from baseColor: Color, steps: Int = 5) -> [Color] {
        var colors: [Color] = []
        
        for i in 0..<steps {
            let ratio = Double(i) / Double(steps - 1)
            let opacity = 1.0 - (ratio * 0.8) // 从不透明到20%透明
            colors.append(baseColor.opacity(opacity))
        }
        
        return colors
    }
    
    // MARK: - 动态主题色
    static func dynamicBackground(for timeOfDay: TimeOfDay) -> Color {
        switch timeOfDay {
        case .morning:
            return Color.orange.opacity(0.3)
        case .afternoon:
            return Color.blue.opacity(0.3)
        case .evening:
            return Color.purple.opacity(0.3)
        case .night:
            return Color.black
        }
    }
}

// MARK: - 时间段枚举
enum TimeOfDay {
    case morning
    case afternoon
    case evening
    case night
    
    static var current: TimeOfDay {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 6..<12:
            return .morning
        case 12..<18:
            return .afternoon
        case 18..<22:
            return .evening
        default:
            return .night
        }
    }
}

// MARK: - 颜色动画支持
extension Color {
    static func interpolate(from: Color, to: Color, progress: Double) -> Color {
        let progress = max(0, min(1, progress))
        return from.blended(with: to, ratio: progress)
    }
}

// MARK: - 调试支持
extension Color {
    func debugDescription() -> String {
        return "Color(hex: \"\(hexString)\", brightness: \(String(format: "%.2f", brightness)))"
    }
}
