//
//  DynamicColorManager.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import SwiftUI
import Combine

// MARK: - 动态颜色管理器
@MainActor
class DynamicColorManager: ObservableObject {
    @Published var currentColors: ExtractedColors = .defaultDark
    @Published var isTransitioning = false
    
    private let colorExtractor = ImageColorExtractor()
    private var cancellables = Set<AnyCancellable>()
    private var currentImageURL: String = ""
    
    // 颜色过渡动画配置
    private let transitionDuration: Double = 0.8
    private let debounceDelay: Double = 0.3
    
    init() {
        setupBindings()
    }
    
    private func setupBindings() {
        // 监听颜色提取器的变化
        colorExtractor.$currentColors
            .receive(on: DispatchQueue.main)
            .sink { [weak self] newColors in
                self?.updateColors(newColors)
            }
            .store(in: &cancellables)
        
        colorExtractor.$isExtracting
            .receive(on: DispatchQueue.main)
            .assign(to: \.isTransitioning, on: self)
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法

    /// 预加载所有图片并提取颜色
    func preloadAllImages(_ imageURLs: [String]) {
        print("🚀 开始预加载 \(imageURLs.count) 张轮播图片...")

        Task.detached(priority: .background) { [weak self] in
            guard let self = self else { return }

            // 限制并发数量，避免内存压力
            let maxConcurrentTasks = 3
            let semaphore = DispatchSemaphore(value: maxConcurrentTasks)

            await withTaskGroup(of: Void.self) { group in
                for (index, imageURL) in imageURLs.enumerated() {
                    group.addTask {
                        await semaphore.wait()
                        defer { semaphore.signal() }

                        await self.colorExtractor.extractColors(from: imageURL)
                        await MainActor.run {
                            print("✅ 预加载完成 (\(index + 1)/\(imageURLs.count)): \(imageURL)")
                        }
                    }
                }
            }

            await MainActor.run {
                print("🎉 所有轮播图片预加载完成！")
            }
        }
    }

    /// 更新图片并提取颜色
    func updateImage(_ imageURL: String) {
        guard imageURL != currentImageURL else { return }

        currentImageURL = imageURL

        // 防抖处理，避免频繁切换时的性能问题
        DispatchQueue.main.asyncAfter(deadline: .now() + debounceDelay) { [weak self] in
            guard let self = self, self.currentImageURL == imageURL else { return }

            Task {
                await self.colorExtractor.extractColors(from: imageURL)
            }
        }
    }
    
    /// 重置为默认颜色
    func resetToDefault() {
        currentImageURL = ""
        updateColors(.defaultDark)
    }
    
    /// 手动设置颜色（用于测试或特殊情况）
    func setColors(_ colors: ExtractedColors) {
        updateColors(colors)
    }
    
    // MARK: - 私有方法
    
    private func updateColors(_ newColors: ExtractedColors) {
        withAnimation(.easeInOut(duration: transitionDuration)) {
            currentColors = newColors
        }
    }
    
    // MARK: - 便捷属性
    
    /// 获取当前的渐变颜色数组
    var gradientColors: [Color] {
        return currentColors.gradientColors
    }
    
    /// 获取主要背景色
    var backgroundColor: Color {
        return currentColors.background
    }
    
    /// 获取主导色
    var dominantColor: Color {
        return currentColors.dominant
    }
    
    /// 获取强调色
    var accentColor: Color {
        return currentColors.accent
    }
    
    /// 检查是否为默认颜色
    var isDefaultColors: Bool {
        return currentColors.background == .black &&
               currentColors.dominant == .black
    }
}

// MARK: - 预设颜色主题
extension DynamicColorManager {
    
    /// 电影类型预设颜色
    static func presetColors(for genre: String) -> ExtractedColors {
        switch genre.lowercased() {
        case "action", "动作":
            return ExtractedColors(
                dominant: Color.red.opacity(0.8),
                secondary: Color.orange.opacity(0.6),
                accent: Color.yellow.opacity(0.4),
                background: Color.black
            )
            
        case "horror", "恐怖":
            return ExtractedColors(
                dominant: Color.black,
                secondary: Color.red.opacity(0.3),
                accent: Color.gray.opacity(0.5),
                background: Color.black
            )
            
        case "romance", "爱情":
            return ExtractedColors(
                dominant: Color.pink.opacity(0.6),
                secondary: Color.purple.opacity(0.4),
                accent: Color.red.opacity(0.3),
                background: Color.black
            )
            
        case "sci-fi", "科幻":
            return ExtractedColors(
                dominant: Color.blue.opacity(0.7),
                secondary: Color.cyan.opacity(0.5),
                accent: Color.teal.opacity(0.3),
                background: Color.black
            )
            
        case "comedy", "喜剧":
            return ExtractedColors(
                dominant: Color.yellow.opacity(0.6),
                secondary: Color.orange.opacity(0.4),
                accent: Color.green.opacity(0.3),
                background: Color.black
            )
            
        default:
            return .defaultDark
        }
    }
    
    /// 根据时间设置主题色
    func setTimeBasedTheme() {
        let hour = Calendar.current.component(.hour, from: Date())
        
        let colors: ExtractedColors
        switch hour {
        case 6..<12: // 早晨
            colors = ExtractedColors(
                dominant: Color.orange.opacity(0.6),
                secondary: Color.yellow.opacity(0.4),
                accent: Color.blue.opacity(0.3),
                background: Color.black
            )
            
        case 12..<18: // 下午
            colors = ExtractedColors(
                dominant: Color.blue.opacity(0.6),
                secondary: Color.cyan.opacity(0.4),
                accent: Color.teal.opacity(0.3),
                background: Color.black
            )
            
        case 18..<22: // 傍晚
            colors = ExtractedColors(
                dominant: Color.purple.opacity(0.6),
                secondary: Color.pink.opacity(0.4),
                accent: Color.orange.opacity(0.3),
                background: Color.black
            )
            
        default: // 夜晚
            colors = ExtractedColors(
                dominant: Color.indigo.opacity(0.6),
                secondary: Color.blue.opacity(0.4),
                accent: Color.purple.opacity(0.3),
                background: Color.black
            )
        }
        
        setColors(colors)
    }
}

// MARK: - 调试和测试支持
extension DynamicColorManager {
    
    /// 测试用的颜色循环
    func cycleTestColors() {
        let testColors = [
            ExtractedColors(
                dominant: Color.red.opacity(0.8),
                secondary: Color.orange.opacity(0.6),
                accent: Color.yellow.opacity(0.4),
                background: Color.black
            ),
            ExtractedColors(
                dominant: Color.blue.opacity(0.8),
                secondary: Color.cyan.opacity(0.6),
                accent: Color.teal.opacity(0.4),
                background: Color.black
            ),
            ExtractedColors(
                dominant: Color.green.opacity(0.8),
                secondary: Color.mint.opacity(0.6),
                accent: Color.yellow.opacity(0.4),
                background: Color.black
            ),
            .defaultDark
        ]
        
        let randomColors = testColors.randomElement() ?? .defaultDark
        setColors(randomColors)
    }
    
    /// 打印当前颜色信息（调试用）
    func printCurrentColors() {
        print("=== 当前颜色信息 ===")
        print("主导色: \(currentColors.dominant)")
        print("次要色: \(currentColors.secondary)")
        print("强调色: \(currentColors.accent)")
        print("背景色: \(currentColors.background)")
        print("是否为默认色: \(isDefaultColors)")
        print("==================")
    }
}
