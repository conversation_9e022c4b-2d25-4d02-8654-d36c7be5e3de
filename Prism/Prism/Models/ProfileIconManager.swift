//
//  ProfileIconManager.swift
//  Prism
//
//  Created by AI Assistant on 2025-07-30.
//

import Foundation
import SwiftUI

class ProfileIconManager: ObservableObject {
    @Published var selectedIcon: String = "03Toblf_d"
    @Published var availableIcons: [String] = []
    
    init() {
        loadAvailableIcons()
    }
    
    private func loadAvailableIcons() {
        // 使用所有已复制到Assets.xcassets中的头像图片
        availableIcons = [
            "03Toblf_d", "0tsTsw8_d", "1AlInff_d", "1SdlYmc_d", "1bOMmJI_d",
            "1r9b1v6_d", "2DuaS2D_d", "2HZX0Lu_d", "2HgkxGM_d", "2TDBDRJ_d",
            "2VdEdvj_d", "2XYml5r_d", "2Xn9a1H_d", "2mcZOnB_d", "2mve2MK_d",
            "2nusc60_d", "2sIkxyH_d", "2zxAhqx_d", "3ANStMe_d", "3CeW28U_d",
            "3HTt894_d", "3SycIUM_d", "3ZtRl1h_d", "488zHsI_d", "4CK4v6W_d",
            "4EFiHC6_d", "4EpWE6H_d", "4HP8NGK_d", "4aTOfLs_d", "4dIo1mc_d",
            "57Q2qzE_d", "5GW8AEy_d", "5UxnQ4A_d", "5aI70l2_d", "6115Vh1_d",
            "6FgZxbi_d", "6NDpYkk_d", "6ZIfuJG_d", "6iIUarA_d", "6m9QQ3d_d",
            "6xvQLJZ_d", "7042hRD_d", "7ukdmbV_d", "89MaCuQ_d", "8XisEz1_d",
            "8rVCbDp_d", "9EmGORl_d", "9VX2XI5_d", "9Wp7txC_d", "9XgJgw3_d",
            "9e1vhoB_d", "9nWtdiZ_d", "9u7HoBe_d", "9uic8GQ_d", "A1QCYys_d",
            "ACHEsdh_d", "AEZWoxn_d", "AEnnDzu_d", "AFN9X8K_d", "APYSZGK_d",
            "AVtM22g_d", "AZBszLs_d", "AiGQewT_d", "AmfRm1D_d", "BU2Bkjf_d",
            "BUhSCW8_d", "BxsbBsu_d", "C39AF1P_d", "CANzj7P_d", "CEorPqU_d",
            "CIlcNgj_d", "CJ6xeX2_d", "CVeu3Ry_d", "CjMherY_d", "CwJgAqo_d",
            "CzL4Sca_d", "DAxiSyF_d", "DTQqnbN_d", "DZvE7M5_d", "DcTxHgK_d",
            "E0WNa28_d", "E4ady6r_d", "EUoIzxs_d", "EoSjS2Y_d", "EpNGsJr_d",
            "F4E6L7c_d", "FY2NkRM_d", "FZZGphW_d", "FdP8OhZ_d", "FmNA3Di_d",
            "FyPs9Fv_d", "GJTw0i2_d", "GULR8QP_d", "GqJYXyv_d", "GyGZmke_d",
            "HChqj4X_d", "HHddxKo_d", "HQMMwgh_d", "HRL7WZB_d", "HqpJk1t_d",
            "HumsssB_d", "I9ns8dV_d", "IFhpAZQ_d", "IHnR5Fo_d", "Igqdb4Q_d",
            "IkQ2XOi_d", "Iki5qJq_d", "IosnqzG_d", "JE2sDKh_d", "JJ5Q3N9_d",
            "JNNQanR_d", "JTlCoLo_d", "JdYIFkw_d", "Jo6qKgc_d", "JpGzLyC_d",
            "KGmPbzZ_d", "KclrcPw_d", "Kkaeq84_d", "KnlPrNg_d", "LJ9dB0T_d",
            "Lh1d2Iv_d", "M07YrRe_d", "M3A4efp_d", "MLR0Ok2_d", "Nj9cuxa_d",
            "NkseE8G_d", "NmdS8eT_d", "NxgX1hO_d", "ObgxjtI_d", "OgA573Z_d",
            "OnOEIhZ_d", "P5xixqL_d", "P7olE25_d", "P8qZD6Y_d", "PJNXrDK_d",
            "PUnmtVf_d", "PWTu3Mh_d", "PrlPHVI_d", "PuAGCyG_d", "QLAh0E5_d",
            "QNCF9b9_d", "QVhPEj1_d", "QhKoEyB_d", "QkrPDpq_d", "QnhmfUC_d",
            "R5tNjg0_d", "RGPayKA_d", "RIpn14T_d", "Rchi9ja_d", "ReuQyZ0_d",
            "Rezt0eb_d", "Rh8hQPy_d", "RjZiWZr_d", "S1m2anG_d", "SBxVcG2_d",
            "SMyax2O_d", "SiJbrKS_d", "T9kO96O_d", "TIUUjEi_d", "TOkfbVh_d",
            "TaKAofO_d", "U5Aima9_d", "UKMYSKj_d", "URztdiE_d", "VCU74jG_d",
            "VFVCfKq_d", "VoOgmDk_d", "VzyGtQt_d", "WCUyEYj_d", "WK2idvp_d",
            "WKTGeaA_d", "WZMl9rm_d", "WfPBb3K_d", "Wj9O64i_d", "WmB8jg2_d",
            "WmzsGXA_d", "XEQRNtc_d", "XQ5uDGB_d", "XRerDoH_d", "XSzYJIX_d",
            "XTIFwVV_d", "YkyLA3e_d", "ZAyj3n2_d", "ZvH6qpn_d", "a8AZiRv_d",
            "aFRnBjW_d", "adBh2wl_d", "adcsC09_d", "anAMekG_d", "ay5uqWM_d",
            "b8eMrMs_d", "bBKeCVv_d", "bIjfX6q_d", "bKUIgZ7_d", "bQYJ3j2_d",
            "bdUPpjF_d", "bq9INZJ_d", "by709tO_d", "cYLT6nN_d", "ccR9oO2_d",
            "cp0Xh0u_d", "cxjXa7S_d", "dO1LjLs_d", "dk4Wyhp_d", "dxixjKh_d",
            "eB9PM6u_d", "eLZuR79_d", "eQHooLm_d", "eWutiu3_d", "erqh144_d",
            "ezKXO5r_d", "f4O1czr_d", "fCO4I1B_d", "fIfmkFI_d", "fjB3rhK_d",
            "fuMao0H_d", "g1c0Dnd_d", "gBbRCL2_d", "gkmgELj_d", "h3TdDQk_d",
            "hDczUXa_d", "hW5CKGC_d", "hYkzmZj_d", "ht2hiNi_d", "i9kgLpg_d",
            "ih6xvXa_d", "ik6iajw_d", "j5Q83Ob_d", "j7d4PaX_d", "jKFveyk_d",
            "jWWmZ4o_d", "jcQ4tp7_d", "jchcK6t_d", "jpCFhWV_d", "kDoJa0M_d",
            "kE94yT1_d", "kq6ELXg_d", "kz2ALK1_d", "l5cGCgZ_d", "lViU8jk_d",
            "lfn5ppt_d", "lk6KMye_d", "lliiZRp_d", "lvpwoXL_d", "mf6WP4D_d",
            "mgCLKUO_d", "n7EbguT_d", "nGrlseY_d", "naDQ1w2_d", "naOJbyA_d",
            "njoEt8J_d", "o8KusRZ_d", "oDZG29e_d", "oJjjfDr_d", "oW50nTa_d",
            "odfPpmC_d", "oecmxZf_d", "ouJt7tr_d", "p19PL8P_d", "pDaRujD_d",
            "pcqZiyO_d", "pdY1P5U_d", "pfNi87l_d", "prnpovw_d", "q439pdg_d",
            "q7aTU9q_d", "r3XvUd6_d", "r868qLv_d", "roiEThv_d", "s0eMiVe_d",
            "sQtzYQV_d", "shFECfe_d", "siylyIN_d", "sjM1iul_d", "stnadtw_d",
            "t1CWBoj_d", "t3eZyH5_d", "tY3gDBM_d", "tkhYPDU_d", "tsBP63Y_d",
            "u1DJo5h_d", "u6zXLig_d", "uRrq2YA_d", "ujKKqFc_d", "v2GNog0_d",
            "v4jDMfG_d", "vJ6wC9G_d", "veAxJmY_d", "vth7PBt_d", "w8p0Arf_d",
            "wBbehpY_d", "wNuh9Ba_d", "wY0naAa_d", "wYQRaaP_d", "wkagqCj_d",
            "wx5phjr_d", "x1Nu4Vz_d", "xA7OId3_d", "xBGWBjQ_d", "xGVD1fh_d",
            "xKzGjFc_d", "xQmG5kx_d", "xaCN943_d", "xbNwu9t_d", "xwlBspT_d",
            "y5Sk51I_d", "yhnwhe1_d", "yqZo7bX_d", "yv2LihI_d", "zYfYlE7_d",
            "zrs2LGz_d"
        ]
    }
    
    func selectIcon(_ iconName: String) {
        selectedIcon = iconName
    }
    
    func getIconImage(_ iconName: String) -> UIImage? {
        // 方法1: 从Resources/ProfileIcons目录加载WebP文件
        if let path = Bundle.main.path(forResource: "Resources/ProfileIcons/\(iconName)", ofType: "webp"),
           let data = NSData(contentsOfFile: path) {
            return UIImage(data: data as Data)
        }

        // 方法2: 尝试从bundle根目录加载
        if let path = Bundle.main.path(forResource: iconName, ofType: "webp"),
           let data = NSData(contentsOfFile: path) {
            return UIImage(data: data as Data)
        }

        // 方法3: 尝试从Assets.xcassets中加载（如果已经转换为imageset）
        if let image = UIImage(named: iconName) {
            return image
        }

        // 方法4: 尝试从原始Data/ProfileIcon目录加载
        if let path = Bundle.main.path(forResource: "Data/ProfileIcon/\(iconName)", ofType: "webp"),
           let data = NSData(contentsOfFile: path) {
            return UIImage(data: data as Data)
        }

        print("Could not find image: \(iconName)")
        return nil
    }
    
    var selectedIconImage: UIImage? {
        return getIconImage(selectedIcon)
    }
}
