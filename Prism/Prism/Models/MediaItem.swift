//
//  MediaItem.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import Foundation

// MARK: - 媒体项目数据模型
struct MediaItem: Codable, Identifiable {
    let id: Int
    let name: String
    let englishName: String
    let level: Int
    let posterURL: String
    let thumbnailURL: String?
    let tag: String
    let category: String
    let remarks: String
    let serial: String
    let score: String
    let doubanScore: String
    let subtitle: String
    let actors: String
    let description: String
    
    // 计算属性：获取演员数组
    var actorList: [String] {
        actors.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }
    }
    
    // 计算属性：获取分类数组
    var categoryList: [String] {
        category.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }
    }
    
    // 计算属性：格式化评分
    var formattedScore: Double {
        Double(score) ?? 0.0
    }
    
    // 计算属性：格式化豆瓣评分
    var formattedDoubanScore: Double {
        Double(doubanScore) ?? 0.0
    }
    
    private enum CodingKeys: String, CodingKey {
        case id = "vod_id"
        case name = "vod_name"
        case englishName = "vod_en"
        case level = "vod_level"
        case posterURL = "vod_pic"
        case thumbnailURL = "vod_pic_thumb"
        case tag = "vod_tag"
        case category = "vod_class"
        case remarks = "vod_remarks"
        case serial = "vod_serial"
        case score = "vod_score"
        case doubanScore = "vod_douban_score"
        case subtitle = "vod_sub"
        case actors = "vod_actor"
        case description = "vod_blurb"
    }
}

// MARK: - API响应模型
struct MediaResponse: Codable {
    let code: Int
    let message: String
    let page: Int
    let pageCount: Int
    let limit: Int
    let total: Int
    let list: [MediaItem]
    let copyright: String
    
    private enum CodingKeys: String, CodingKey {
        case code
        case message = "msg"
        case page
        case pageCount = "pagecount"
        case limit
        case total
        case list
        case copyright
    }
}

// MARK: - 轮播图数据模型
struct CarouselItem: Identifiable {
    let id = UUID()
    let mediaItem: MediaItem
    let releaseDate: String
    let backdropURL: String?
    
    init(mediaItem: MediaItem, releaseDate: String = "2025-07-23") {
        self.mediaItem = mediaItem
        self.releaseDate = releaseDate
        self.backdropURL = mediaItem.posterURL
    }
}

// MARK: - 内容分区模型
struct ContentSection: Identifiable {
    let id = UUID()
    let title: String
    let items: [MediaItem]
    let sectionType: SectionType
    
    enum SectionType {
        case todayTrending
        case weekTrending
        case recommended
        case newReleases
    }
}

// MARK: - 模拟数据管理器
class MockDataManager: ObservableObject {
    @Published var featuredItems: [CarouselItem] = []
    @Published var todayTrending: [MediaItem] = []
    @Published var weekTrending: [MediaItem] = []
    @Published var isLoading = false
    
    init() {
        loadMockData()
    }
    
    func loadMockData() {
        // 尝试多种方式加载mock.json文件
        if let data = loadMockDataFromBundle() {
            decodeMockData(data)
            return
        }

        // 如果无法加载，创建示例数据
        print("无法加载mock.json，使用示例数据")
        createSampleData()
    }

    private func loadMockDataFromBundle() -> Data? {
        // 方法1: 从主Bundle加载
        if let url = Bundle.main.url(forResource: "mock", withExtension: "json") {
            print("找到mock.json文件路径: \(url.path)")
            if let data = try? Data(contentsOf: url) {
                print("成功从Bundle加载mock.json")
                return data
            }
        }

        // 方法2: 尝试从项目目录加载
        let projectPaths = [
            "Data/Mock/mock.json",
            "Prism/Data/Mock/mock.json",
            "mock.json"
        ]

        for path in projectPaths {
            if let url = Bundle.main.url(forResource: path, withExtension: nil),
               let data = try? Data(contentsOf: url) {
                print("成功从路径加载mock.json: \(path)")
                return data
            }
        }

        print("无法找到mock.json文件")
        return nil
    }

    private func decodeMockData(_ data: Data) {
        do {
            let response = try JSONDecoder().decode(MediaResponse.self, from: data)
            let mediaItems = response.list

            // 创建轮播图数据（取前5个）
            featuredItems = Array(mediaItems.prefix(5)).map { item in
                CarouselItem(mediaItem: item)
            }

            // 今日趋势（取6-15个）
            todayTrending = Array(mediaItems.dropFirst(5).prefix(10))

            // 本周趋势（取16-25个）
            weekTrending = Array(mediaItems.dropFirst(15).prefix(10))

        } catch {
            print("Failed to decode mock data: \(error)")
            createSampleData()
        }
    }

    private func createSampleData() {
        // 创建示例数据
        let sampleItems = [
            MediaItem(
                id: 1,
                name: "神奇4侠：初露锋芒",
                englishName: "fantastic-four",
                level: 0,
                posterURL: "https://img.ffzy888.com/upload/vod/20250612-1/785d460852190e40d6f55c1aa19fab3f.jpg",
                thumbnailURL: nil,
                tag: "",
                category: "动作,科幻",
                remarks: "HD",
                serial: "0",
                score: "8.5",
                doubanScore: "8.0",
                subtitle: "",
                actors: "佩德罗·帕斯卡,凡妮莎·柯比",
                description: "神奇4侠：初露锋芒故事设定在以1960年代为灵感的复古未来世界，讲述了四位年轻的外来者，他们被传送到一个危险的宇宙中，身体发生了巨大的变化。"
            ),
            MediaItem(
                id: 2,
                name: "破果",
                englishName: "poguo",
                level: 0,
                posterURL: "https://img.ffzy888.com/upload/vod/20250612-1/785d460852190e40d6f55c1aa19fab3f.jpg",
                thumbnailURL: nil,
                tag: "",
                category: "动作,电影",
                remarks: "HD中字",
                serial: "0",
                score: "7.5",
                doubanScore: "7.0",
                subtitle: "",
                actors: "李慧英,金圣喆,金武烈",
                description: "该片根据同名小说改编，讲述工作果断的60代女莎手「爪角」碰到了一位男性总是给她打造她人生中没有的东西，然后陷入苦恼的故事。"
            )
        ]

        featuredItems = sampleItems.map { CarouselItem(mediaItem: $0) }
        todayTrending = sampleItems
        weekTrending = sampleItems
    }
}
