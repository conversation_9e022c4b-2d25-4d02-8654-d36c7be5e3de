//
//  ImageColorExtractor.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import SwiftUI
import UIKit
import Combine

// MARK: - 颜色提取结果
struct ExtractedColors {
    let dominant: Color
    let secondary: Color
    let accent: Color
    let background: Color
    
    // 用于渐变的颜色数组（保持原有样式的透明度渐变）
    var gradientColors: [Color] {
        return [
            background.opacity(0.0),  // 顶部透明
            background.opacity(0.1),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 中间
            background.opacity(1.0),  // 底部
        ]
    }
    
    // 默认黑色主题
    static let defaultDark = ExtractedColors(
        dominant: .black,
        secondary: .black,
        accent: .black,
        background: .black
    )
}

// MARK: - 图片颜色提取器
@MainActor
class ImageColorExtractor: ObservableObject {
    @Published var currentColors: ExtractedColors = .defaultDark
    @Published var isExtracting = false
    
    private var cancellables = Set<AnyCancellable>()
    private let cache = NSCache<NSString, UIImage>()
    
    init() {
        setupCache()
    }
    
    private func setupCache() {
        cache.countLimit = 50
        cache.totalCostLimit = 50 * 1024 * 1024 // 50MB
    }
    
    // MARK: - 主要提取方法
    func extractColors(from imageURL: String) async {
        guard !imageURL.isEmpty else {
            await MainActor.run {
                self.currentColors = .defaultDark
            }
            return
        }
        
        await MainActor.run {
            self.isExtracting = true
        }
        
        do {
            let colors = try await performColorExtraction(from: imageURL)
            await MainActor.run {
                self.currentColors = colors
                self.isExtracting = false
            }
        } catch {
            print("颜色提取失败: \(error)")
            await MainActor.run {
                self.currentColors = .defaultDark
                self.isExtracting = false
            }
        }
    }
    
    // MARK: - 执行颜色提取
    private func performColorExtraction(from imageURL: String) async throws -> ExtractedColors {
        // 1. 下载或获取缓存的图片
        let image = try await downloadImage(from: imageURL)
        
        // 2. 调整图片大小以提高性能
        let resizedImage = resizeImage(image, to: CGSize(width: 150, height: 150))
        
        // 3. 提取主要颜色
        let dominantColors = extractDominantColors(from: resizedImage)
        
        // 4. 分析颜色并创建配色方案
        return createColorScheme(from: dominantColors)
    }
    
    // MARK: - 图片下载
    private func downloadImage(from urlString: String) async throws -> UIImage {
        let cacheKey = NSString(string: urlString)
        
        // 检查缓存
        if let cachedImage = cache.object(forKey: cacheKey) {
            return cachedImage
        }
        
        // 下载图片
        guard let url = URL(string: urlString) else {
            throw ColorExtractionError.invalidURL
        }
        
        let (data, _) = try await URLSession.shared.data(from: url)
        
        guard let image = UIImage(data: data) else {
            throw ColorExtractionError.invalidImageData
        }
        
        // 缓存图片
        cache.setObject(image, forKey: cacheKey)
        
        return image
    }
    
    // MARK: - 图片大小调整
    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: size))
        }
    }
    
    // MARK: - 提取主要颜色
    private func extractDominantColors(from image: UIImage) -> [UIColor] {
        guard let cgImage = image.cgImage else { return [] }
        
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        
        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)
        
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
        )
        
        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        
        // 颜色统计
        var colorCounts: [String: Int] = [:]
        
        // 采样像素（每隔几个像素采样一次以提高性能）
        let sampleRate = max(1, min(width, height) / 50)
        
        for y in stride(from: 0, to: height, by: sampleRate) {
            for x in stride(from: 0, to: width, by: sampleRate) {
                let pixelIndex = (y * width + x) * bytesPerPixel
                
                if pixelIndex + 2 < pixelData.count {
                    let r = pixelData[pixelIndex]
                    let g = pixelData[pixelIndex + 1]
                    let b = pixelData[pixelIndex + 2]
                    
                    // 量化颜色以减少颜色数量
                    let quantizedR = (r / 32) * 32
                    let quantizedG = (g / 32) * 32
                    let quantizedB = (b / 32) * 32
                    
                    let colorKey = "\(quantizedR)-\(quantizedG)-\(quantizedB)"
                    colorCounts[colorKey, default: 0] += 1
                }
            }
        }
        
        // 获取最常见的颜色
        let sortedColors = colorCounts.sorted { $0.value > $1.value }
        
        var dominantColors: [UIColor] = []
        for (colorKey, _) in sortedColors.prefix(8) {
            let components = colorKey.split(separator: "-").compactMap { UInt8($0) }
            if components.count == 3 {
                let color = UIColor(
                    red: CGFloat(components[0]) / 255.0,
                    green: CGFloat(components[1]) / 255.0,
                    blue: CGFloat(components[2]) / 255.0,
                    alpha: 1.0
                )
                dominantColors.append(color)
            }
        }
        
        return dominantColors
    }
    
    // MARK: - 创建配色方案
    private func createColorScheme(from colors: [UIColor]) -> ExtractedColors {
        guard !colors.isEmpty else {
            return .defaultDark
        }
        
        // 过滤掉过于明亮的颜色（我们需要深色背景）
        let darkColors = colors.filter { color in
            let brightness = color.brightness
            return brightness < 0.7 // 只保留相对较暗的颜色
        }
        
        let workingColors = darkColors.isEmpty ? colors : darkColors
        
        // 选择主要颜色
        let dominant = workingColors.first ?? UIColor.black
        let secondary = workingColors.count > 1 ? workingColors[1] : dominant.darker()
        let accent = workingColors.count > 2 ? workingColors[2] : dominant.lighter()
        let background = workingColors.last?.darker() ?? UIColor.black
        
        return ExtractedColors(
            dominant: Color(dominant),
            secondary: Color(secondary),
            accent: Color(accent),
            background: Color(background)
        )
    }
}

// MARK: - 错误类型
enum ColorExtractionError: Error, LocalizedError {
    case invalidURL
    case invalidImageData
    case extractionFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的图片URL"
        case .invalidImageData:
            return "无效的图片数据"
        case .extractionFailed:
            return "颜色提取失败"
        }
    }
}

// MARK: - UIColor 扩展
extension UIColor {
    var brightness: CGFloat {
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        return (red * 0.299 + green * 0.587 + blue * 0.114)
    }
    
    func darker(by percentage: CGFloat = 0.3) -> UIColor {
        return self.adjustBrightness(by: -percentage)
    }
    
    func lighter(by percentage: CGFloat = 0.3) -> UIColor {
        return self.adjustBrightness(by: percentage)
    }
    
    private func adjustBrightness(by percentage: CGFloat) -> UIColor {
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        return UIColor(
            red: max(0, min(1, red + percentage)),
            green: max(0, min(1, green + percentage)),
            blue: max(0, min(1, blue + percentage)),
            alpha: alpha
        )
    }
}
