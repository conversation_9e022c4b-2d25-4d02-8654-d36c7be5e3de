//
//  HomeView.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import SwiftUI

struct HomeView: View {
    @StateObject private var dataManager = TMDBDataManager()
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景色
                Color(hex: "#1a4b73")
                    .ignoresSafeArea(.all)
                
                if dataManager.featuredItems.isEmpty && dataManager.isLoading {
                    // 加载状态
                    loadingView
                } else if dataManager.featuredItems.isEmpty && dataManager.hasError {
                    // 错误状态
                    errorView
                } else {
                    // 主要内容
                    mainContent
                }

                // 错误提示浮层
                if dataManager.hasError && !dataManager.featuredItems.isEmpty {
                    errorToast
                }
            }
        }
        .navigationBarHidden(true)
    }
    
    // MARK: - 加载视图
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(.white)
            
            Text("加载中...")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
        }
    }
    
    // MARK: - 错误视图
    private var errorView: some View {
        VStack(spacing: 20) {
            Image(systemName: "wifi.slash")
                .font(.system(size: 50))
                .foregroundColor(.white.opacity(0.6))

            Text("网络连接失败")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.white)

            if let errorMessage = dataManager.errorMessage {
                Text(errorMessage)
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }

            Button("重试") {
                Task {
                    await dataManager.refreshAllData()
                }
            }
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 30)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.white.opacity(0.2))
            )
        }
    }

    // MARK: - 错误提示浮层
    private var errorToast: some View {
        VStack {
            Spacer()

            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)

                Text(dataManager.errorMessage ?? "发生错误")
                    .font(.system(size: 14))
                    .foregroundColor(.white)

                Spacer()

                Button("关闭") {
                    dataManager.clearError()
                }
                .font(.system(size: 12))
                .foregroundColor(.blue)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.black.opacity(0.8))
            )
            .padding(.horizontal)
            .padding(.bottom, 100)
        }
        .transition(.move(edge: .bottom))
        .animation(.easeInOut, value: dataManager.hasError)
    }

    // MARK: - 主要内容
    private var mainContent: some View {
        PullToZoomCarouselView(items: dataManager.featuredItems)
    }

}

#Preview {
    HomeView()
}
