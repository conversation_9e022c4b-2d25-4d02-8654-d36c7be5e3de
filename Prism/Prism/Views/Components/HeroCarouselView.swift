//
//  HeroCarouselView.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import SwiftUI

struct HeroCarouselView: View {
    let items: [CarouselItem]
    @State private var currentIndex = 0
    @State private var scrollOffset: CGFloat = 0

    // 轮播图配置
    private let carouselHeight: CGFloat = 400
    private let maxZoomScale: CGFloat = 1.3
    private let pullThreshold: CGFloat = 100
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView(.vertical, showsIndicators: false) {
                VStack(spacing: 0) {
                    // 轮播图区域
                    carouselSection(geometry: geometry)

                    // 内容区域占位
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: 1000) // 用于测试下拉效果
                }
                .background(
                    GeometryReader { geo in
                        Color.clear.preference(
                            key: ScrollOffsetPreferenceKey.self,
                            value: geo.frame(in: .named("scroll")).minY
                        )
                    }
                )
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                scrollOffset = value
            }
        }
        .ignoresSafeArea(.all, edges: .top) // 沉浸式效果
    }
    
    @ViewBuilder
    private func carouselSection(geometry: GeometryProxy) -> some View {
        ZStack {
            // 沉浸式背景图片
            immersiveBackground

            // 装饰线条
            decorativeLines

            // 轮播内容
            carouselContent(geometry: geometry)

            // 顶部导航栏
            topNavigationBar
        }
        .frame(height: carouselHeight + max(0, -scrollOffset * 0.3))
        .scaleEffect(calculateZoomScale(), anchor: .top)
        .animation(.interactiveSpring(response: 0.6, dampingFraction: 0.8), value: scrollOffset)
    }
    
    // MARK: - 沉浸式背景
    private var immersiveBackground: some View {
        Group {
            if !items.isEmpty {
                AsyncImage(url: URL(string: items[currentIndex].mediaItem.posterURL)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .overlay(
                            // 深色遮罩确保文字可读性
                            LinearGradient(
                                colors: [
                                    Color.black.opacity(0.6),
                                    Color.black.opacity(0.3),
                                    Color.black.opacity(0.8)
                                ],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .animation(.easeInOut(duration: 0.5), value: currentIndex)
                } placeholder: {
                    backgroundGradient
                }
                .id(currentIndex)
            } else {
                backgroundGradient
            }
        }
    }

    // MARK: - 背景渐变
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color(hex: "#1a4b73"),
                Color(hex: "#2d5a87"),
                Color(hex: "#4a7ba7")
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    }
    
    // MARK: - 装饰线条
    private var decorativeLines: some View {
        GeometryReader { geometry in
            ForEach(0..<8, id: \.self) { index in
                Path { path in
                    let startX = CGFloat(index) * 60 - 100
                    let startY: CGFloat = -50
                    let endX = startX + geometry.size.width + 200
                    let endY = geometry.size.height + 100
                    
                    path.move(to: CGPoint(x: startX, y: startY))
                    path.addLine(to: CGPoint(x: endX, y: endY))
                }
                .stroke(
                    Color.white.opacity(0.1),
                    style: StrokeStyle(lineWidth: 2, lineCap: .round)
                )
            }
        }
    }
    
    // MARK: - 轮播内容
    @ViewBuilder
    private func carouselContent(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            Spacer()
            
            // 轮播图片和内容
            TabView(selection: $currentIndex) {
                ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                    carouselItemView(item: item)
                        .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .frame(height: 280)
            
            // 页面指示器
            pageIndicator
            
            Spacer().frame(height: 20)
        }
    }
    
    // MARK: - 轮播项视图
    @ViewBuilder
    private func carouselItemView(item: CarouselItem) -> some View {
        VStack(spacing: 20) {
            Spacer()

            // 电影标题
            Text(item.mediaItem.name)
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)

            // 英文标题
            if !item.mediaItem.englishName.isEmpty {
                Text(item.mediaItem.englishName.uppercased())
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .tracking(2)
                    .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
            }

            // 分类和评分
            HStack(spacing: 16) {
                // 分类标签
                Text(item.mediaItem.category)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.white.opacity(0.2))
                    .cornerRadius(15)

                // 评分
                if item.mediaItem.formattedScore > 0 {
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.system(size: 12))
                        Text(String(format: "%.1f", item.mediaItem.formattedScore))
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.black.opacity(0.3))
                    .cornerRadius(15)
                }
            }

            // 描述文本
            Text(item.mediaItem.description)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .lineLimit(3)
                .padding(.horizontal, 40)
                .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)

            Spacer()
        }
    }
    
    // MARK: - 页面指示器
    private var pageIndicator: some View {
        HStack(spacing: 12) {
            ForEach(0..<items.count, id: \.self) { index in
                Circle()
                    .fill(index == currentIndex ? Color.white : Color.white.opacity(0.3))
                    .frame(width: 8, height: 8)
                    .animation(.easeInOut(duration: 0.3), value: currentIndex)
            }
        }
        .padding(.top, 16)
    }
    
    // MARK: - 顶部导航栏
    private var topNavigationBar: some View {
        VStack {
            HStack {
                Spacer()
                
                // 搜索按钮
                Button(action: {}) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(Color.white.opacity(0.2))
                        .clipShape(Circle())
                }
                
                // 用户头像
                Button(action: {}) {
                    Circle()
                        .fill(Color.white.opacity(0.2))
                        .frame(width: 40, height: 40)
                        .overlay(
                            Image(systemName: "person.fill")
                                .font(.system(size: 18))
                                .foregroundColor(.white)
                        )
                        .overlay(
                            Circle()
                                .stroke(Color.white, lineWidth: 2)
                        )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 50) // 状态栏高度
            
            Spacer()
        }
    }
    
    // MARK: - 计算缩放比例
    private func calculateZoomScale() -> CGFloat {
        if scrollOffset < 0 {
            // 下拉时放大，从顶部中心向四周放大
            let pullDistance = abs(scrollOffset)
            let maxPull: CGFloat = 100  // 减小阈值，让效果更容易触发
            let normalizedPull = min(pullDistance / maxPull, 1.0)
            let scaleIncrease = normalizedPull * 0.5  // 增加缩放幅度
            return 1 + scaleIncrease
        }
        return 1.0
    }
}

// MARK: - 滚动偏移量监听
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}



#Preview {
    let mockManager = MockDataManager()
    return HeroCarouselView(items: mockManager.featuredItems)
}
