//
//  PullToZoomCarouselView.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import SwiftUI

struct PullToZoomCarouselView: View {
    let items: [CarouselItem]
    @State private var currentIndex = 0
    @State private var scrollOffset: CGFloat = 0
    @State private var dragOffset: CGFloat = 0  // 滑动偏移量
    @State private var isDragging = false       // 是否正在拖拽
    @StateObject private var profileIconManager = ProfileIconManager()
    @StateObject private var colorManager = DynamicColorManager()
    @State private var showingIconPicker = false

    // MARK: - 轮播图尺寸配置
    private let carouselHeight: CGFloat = 450        // 轮播区域总高度（现在主要用于底部内容定位）
    private let tabViewHeight: CGFloat = 280         // TabView内容高度
    private let maxZoomScale: CGFloat = 1.5
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 最底层：固定的上半部分背景图片
                stretchableBackgroundLayer

                // 主要内容层
                ScrollView(.vertical, showsIndicators: false) {
                    ZStack {
                        // 蒙层：跟随内容滚动
                        bottomMaskLayer

                        VStack(spacing: 0) {
                            // 轮播图内容区域（只有图片，不包含文字）
                            carouselImageLayer
                            .background(
                                GeometryReader { scrollProxy in
                                    Color.clear.preference(
                                        key: ScrollOffsetPreferenceKey.self,
                                        value: scrollProxy.frame(in: .named("scroll")).minY
                                    )
                                }
                            )

                            // 文字和内容区域作为一个整体
                            textAndContentSection
                        }
                    }
                }
                .coordinateSpace(name: "scroll")
                .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                    scrollOffset = value
                }

                // 固定的顶部导航栏
                VStack {
                    fixedTopNavigation
                    Spacer()
                }

                // 悬浮底部导航栏
                VStack {
                    Spacer()
                    floatingBottomTabBar
                        .padding(.horizontal, 20)
                        .padding(.bottom, 0) // 距离底部安全区域的距离
                }
            }
        }
        .ignoresSafeArea(.all, edges: .top)
        .sheet(isPresented: $showingIconPicker) {
            ProfileIconPickerView(
                iconManager: profileIconManager,
                isPresented: $showingIconPicker
            )
        }
        .onChange(of: currentIndex) { oldValue, newValue in
            // 当轮播图切换时，提取新图片的颜色
            if !items.isEmpty && newValue < items.count {
                let imageURL = items[newValue].mediaItem.posterURL
                print("🖼️ 轮播图切换 - 索引: \(newValue), 图片地址: \(imageURL)")
                colorManager.updateImage(imageURL)
            }
        }
        .onAppear {
            if !items.isEmpty {
                // 预加载所有轮播图片
                let allImageURLs = items.map { $0.mediaItem.posterURL }
                colorManager.preloadAllImages(allImageURLs)

                // 立即加载第一张图片的颜色
                let imageURL = items[0].mediaItem.posterURL
                print("🚀 轮播图初始加载 - 索引: 0, 图片地址: \(imageURL)")
                colorManager.updateImage(imageURL)
            }
        }
    }

    // MARK: - 可拉伸背景层
    private var stretchableBackgroundLayer: some View {
        GeometryReader { geometry in
            let screenWidth = geometry.size.width
            let screenHeight = geometry.size.height
            let backgroundHeight = screenHeight * 0.8  // 上半部分高度（80%）

            let pullDistance = max(0, scrollOffset)
            let scaleRatio = 1.0 + (pullDistance / 300.0)

            VStack(spacing: 0) {
                // 上半部分：背景图片区域
                ZStack {
                    if !items.isEmpty {
                        AsyncImage(url: URL(string: items[currentIndex].mediaItem.posterURL)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)  // 保持原始比例
                                .frame(width: screenWidth)  // 只设置宽度，高度自动计算
                                .scaleEffect(scaleRatio, anchor: .top)
                                .animation(.easeInOut(duration: 0.5), value: currentIndex)
                        } placeholder: {
                            Color.black
                                .frame(width: screenWidth, height: 400)  // 占位符使用固定高度
                                .scaleEffect(scaleRatio, anchor: .top)
                        }
                    } else {
                        Color.black
                            .frame(width: screenWidth, height: 400)  // 默认背景使用黑色
                            .scaleEffect(scaleRatio, anchor: .top)
                    }
                }
                .frame(height: backgroundHeight, alignment: .top)  // 容器高度可调整，图片对齐到顶部
                .clipped()  // 裁剪超出部分

                Spacer()
            }
        }
    }

    // MARK: - 轮播图片层（只有图片）
    private var carouselImageLayer: some View {
        GeometryReader { geometry in
            let minY = geometry.frame(in: .global).minY

            ZStack {
                // 透明背景，让底层的背景图片显示
                Color.clear

                // 轮播内容
                carouselContent
            }
            .onChange(of: minY) { oldValue, newValue in
                scrollOffset = newValue
            }
        }
        .frame(height: carouselHeight)
    }


    

    
    // MARK: - 轮播内容（只有图片）
    private var carouselContent: some View {
        VStack(spacing: 0) {
            Spacer()

            if !items.isEmpty {
                TabView(selection: $currentIndex) {
                    ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                        carouselImageItem(item)
                            .offset(x: isDragging ? dragOffset : 0)  // 添加滑动偏移效果
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .frame(height: tabViewHeight)
                .gesture(
                    DragGesture()
                        .onChanged { value in
                            isDragging = true
                            dragOffset = value.translation.width
                        }
                        .onEnded { value in
                            isDragging = false
                            dragOffset = 0

                            // 根据滑动距离和速度判断是否切换页面
                            let threshold: CGFloat = 50
                            let velocity = value.predictedEndTranslation.width - value.translation.width

                            if abs(value.translation.width) > threshold || abs(velocity) > 100 {
                                if value.translation.width > 0 && currentIndex > 0 {
                                    // 向右滑动，切换到上一张
                                    currentIndex -= 1
                                } else if value.translation.width < 0 && currentIndex < items.count - 1 {
                                    // 向左滑动，切换到下一张
                                    currentIndex += 1
                                }
                            }
                        }
                )
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isDragging)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: dragOffset)
            }

            // 当前轮播项的文字描述 - 放在分页器上面
            if !items.isEmpty && currentIndex < items.count {
                Text(items[currentIndex].mediaItem.description)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                    .padding(.horizontal, 40)
                    .padding(.top, 10)
                    .padding(.bottom, 10)
                    .shadow(color: .black.opacity(0.7), radius: 3, x: 0, y: 2)
            }

            // 页面指示器
            if items.count > 1 {
                pageIndicator
            }

            Spacer().frame(height: 20)
        }
    }
    
    // MARK: - 轮播图片项（只有图片）
    @ViewBuilder
    private func carouselImageItem(_ item: CarouselItem) -> some View {
        // 只显示图片，不包含文字
        Color.clear
    }

    // MARK: - 内容区域（不包含文字）
    private var textAndContentSection: some View {
        // 只有内容区域，文字已经移到轮播图内部
        contentSection
    }

    // MARK: - 底部蒙层
    private var bottomMaskLayer: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 透明区域，不遮挡轮播图上部分
                Color.clear
                    .frame(height: carouselHeight * 0.1)  // 从轮播图10%位置开始

                // 动态颜色渐变蒙层，保持原有的透明度渐变样式
                LinearGradient(
                    colors: [
                        colorManager.backgroundColor.opacity(0.0),  // 顶部透明
                        colorManager.backgroundColor.opacity(0.1),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 中间
                        colorManager.backgroundColor.opacity(1.0),  // 底部
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .frame(height: max(geometry.size.height * 2, 2000))  // 确保足够高度覆盖所有内容
                .animation(.easeInOut(duration: 0.8), value: colorManager.currentColors.background)
            }
        }
        .allowsHitTesting(false)  // 不阻挡用户交互
    }

    // MARK: - 页面指示器
    private var pageIndicator: some View {
        HStack(spacing: 12) {
            ForEach(0..<items.count, id: \.self) { index in
                Circle()
                    .fill(index == currentIndex ? Color.white : Color.white.opacity(0.3))
                    .frame(width: 8, height: 8)
                    .animation(.easeInOut(duration: 0.3), value: currentIndex)
            }
        }
        .padding(.top, 16)
    }
    
    // MARK: - 固定顶部导航
    private var fixedTopNavigation: some View {
        HStack {
            // Logo图标 - 无背景，细线条
            Button(action: {}) {
                Image(systemName: "tv")
                    .font(.system(size: 20, weight: .thin))
                    .foregroundColor(.white)
                    .frame(width: 40, height: 40)
            }

            Spacer()

            // 右侧按钮组
            HStack(spacing: 20) {
                // 搜索按钮 - 无背景，细线条
                Button(action: {}) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 20, weight: .thin))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                }

                // 头像按钮
                Button(action: {
                    showingIconPicker = true
                }) {
                    Group {
                        if let selectedImage = profileIconManager.selectedIconImage {
                            Image(uiImage: selectedImage)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } else {
                            // 备用图标
                            Image(systemName: "person.crop.circle.fill")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(.white)
                        }
                    }
                    .frame(width: 28, height: 28)
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
                }
            }
            .padding(.trailing, 6) // 将整个右侧按钮组向左移动
        }
        .padding(.horizontal, 20)
        .padding(.top, 50)
    }

    // MARK: - 原始顶部导航（保留以防需要）
    private var topNavigation: some View {
        VStack {
            HStack {
                Spacer()

                Button(action: {}) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(Color.white.opacity(0.2))
                        .clipShape(Circle())
                }

                Button(action: {}) {
                    Circle()
                        .fill(Color.white.opacity(0.2))
                        .frame(width: 40, height: 40)
                        .overlay(
                            Image(systemName: "person.fill")
                                .font(.system(size: 18))
                                .foregroundColor(.white)
                        )
                        .overlay(
                            Circle().stroke(Color.white, lineWidth: 2)
                        )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 50)

            Spacer()
        }
    }
    
    // MARK: - 内容区域
    private var contentSection: some View {
        VStack(spacing: 20) {
            ForEach(0..<10) { _ in
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.1))
                    .frame(height: 100)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.top, 20)
        // 移除原来的蓝色背景，使用蒙层作为背景
    }
    
    // MARK: - 悬浮底部导航栏
    private var floatingBottomTabBar: some View {
        GeometryReader { geometry in
            HStack(spacing: 0) {
                FloatingTabBarItem(icon: "house.fill", title: "发现", isSelected: true)
                FloatingTabBarItem(icon: "play.rectangle", title: "追剧", isSelected: false)
                FloatingTabBarItem(icon: "gearshape.fill", title: "设置", isSelected: false)
            }
            .frame(width: geometry.size.width * 0.55) // 宽度为屏幕宽度的45%
            .padding(.horizontal, 10)
            .padding(.vertical, 8)
            .background(
                Capsule() // 使用胶囊形状，自动适应高度的完美圆角
                    .fill(.ultraThinMaterial)
                    .overlay(
                        Capsule()
                            .fill(Color.black.opacity(0.2))
                    )
            )
            .shadow(color: .black.opacity(0.15), radius: 12, x: 0, y: 6)
            .frame(maxWidth: .infinity) // 在父容器中居中
        }
        .frame(height: 60) // 给GeometryReader一个固定高度
    }

    // MARK: - 原始底部导航栏（保留）
    private var bottomTabBar: some View {
        HStack {
            TabBarItem(icon: "house.fill", title: "发现", isSelected: true)
            Spacer()
            TabBarItem(icon: "play.rectangle", title: "追剧", isSelected: false)
            Spacer()
            TabBarItem(icon: "gearshape.fill", title: "设置", isSelected: false)
        }
        .padding(.horizontal, 40)
        .padding(.vertical, 16)
        .background(
            Color.black
                .opacity(0.9)
                .background(.ultraThinMaterial)
        )
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(Color.white.opacity(0.2)),
            alignment: .top
        )
        .ignoresSafeArea(.all, edges: .bottom)
    }
    

}

// MARK: - 悬浮导航栏项
struct FloatingTabBarItem: View {
    let icon: String
    let title: String
    let isSelected: Bool

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(isSelected ? .white : .white.opacity(0.6))

            Text(title)
                .font(.system(size: 10, weight: isSelected ? .semibold : .regular))
                .foregroundColor(isSelected ? .white : .white.opacity(0.6))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 4)
        .padding(.horizontal, 2)
        .background(
            Capsule() // 选中状态也使用胶囊形状
                .fill(isSelected ? Color.white.opacity(0.25) : Color.clear)
                .padding(.horizontal, -6) // 负内边距让背景向外扩展
                .padding(.vertical, -4)   // 负内边距让背景向外扩展
        )
        .animation(.easeInOut(duration: 0.3), value: isSelected)
    }
}

// MARK: - 原始底部导航栏项
struct TabBarItem: View {
    let icon: String
    let title: String
    let isSelected: Bool

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(isSelected ? .white : .white.opacity(0.6))

            Text(title)
                .font(.system(size: 12, weight: isSelected ? .medium : .regular))
                .foregroundColor(isSelected ? .white : .white.opacity(0.6))
        }
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

#Preview {
    let tmdbManager = TMDBDataManager()
    return PullToZoomCarouselView(items: tmdbManager.featuredItems)
}
