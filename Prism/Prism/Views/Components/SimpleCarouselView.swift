//
//  SimpleCarouselView.swift
//  Prism
//
//  Created by 柏森森seng on 2025/7/30.
//

import SwiftUI

struct SimpleCarouselView: View {
    let items: [CarouselItem]
    @State private var currentIndex = 0
    @State private var dragOffset: CGFloat = 0
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView(.vertical, showsIndicators: false) {
                VStack(spacing: 0) {
                    // 轮播图区域
                    carouselSection(geometry: geometry)

                    // 内容区域
                    contentSection
                }
                .background(
                    GeometryReader { geo in
                        Color.clear.preference(
                            key: ScrollOffsetPreferenceKey.self,
                            value: geo.frame(in: .named("scroll")).minY
                        )
                    }
                )
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                dragOffset = value
            }
        }
        .ignoresSafeArea(.all, edges: .top)
    }
    
    @ViewBuilder
    private func carouselSection(geometry: GeometryProxy) -> some View {
        ZStack {
            // 背景图片（沉浸式）
            if !items.isEmpty {
                AsyncImage(url: URL(string: items[currentIndex].mediaItem.posterURL)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .overlay(
                            // 深色遮罩确保文字可读性
                            LinearGradient(
                                colors: [
                                    Color.black.opacity(0.6),
                                    Color.black.opacity(0.3),
                                    Color.black.opacity(0.8)
                                ],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .animation(.easeInOut(duration: 0.5), value: currentIndex)
                } placeholder: {
                    // 加载时的渐变背景
                    LinearGradient(
                        colors: [
                            Color(hex: "#1a4b73"),
                            Color(hex: "#2d5a87"),
                            Color(hex: "#4a7ba7")
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                }
                .id(currentIndex) // 强制重新加载图片
            } else {
                // 默认渐变背景
                LinearGradient(
                    colors: [
                        Color(hex: "#1a4b73"),
                        Color(hex: "#2d5a87"),
                        Color(hex: "#4a7ba7")
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            }

            // 装饰线条
            decorativePattern

            // 轮播内容
            carouselContent

            // 顶部导航
            topNavigation
        }
        .frame(height: 400 + max(0, -dragOffset * 0.3))
        .scaleEffect(calculateScale(), anchor: .top)
        .animation(.interactiveSpring(response: 0.6, dampingFraction: 0.8), value: dragOffset)
        .clipped()
    }
    
    private var decorativePattern: some View {
        GeometryReader { geometry in
            ForEach(0..<6, id: \.self) { index in
                Path { path in
                    let spacing: CGFloat = 80
                    let startX = CGFloat(index) * spacing - 100
                    let startY: CGFloat = -50
                    let endX = startX + geometry.size.width + 200
                    let endY = geometry.size.height + 100
                    
                    path.move(to: CGPoint(x: startX, y: startY))
                    path.addLine(to: CGPoint(x: endX, y: endY))
                }
                .stroke(Color.white.opacity(0.1), lineWidth: 2)
            }
        }
    }
    
    private var carouselContent: some View {
        VStack(spacing: 0) {
            Spacer()
            
            if !items.isEmpty {
                TabView(selection: $currentIndex) {
                    ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                        carouselItem(item)
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .frame(height: 280)
            }
            
            // 页面指示器
            if items.count > 1 {
                pageIndicator
            }
            
            Spacer().frame(height: 20)
        }
    }
    
    @ViewBuilder
    private func carouselItem(_ item: CarouselItem) -> some View {
        VStack(spacing: 20) {
            Spacer()

            // 电影标题
            Text(item.mediaItem.name)
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)

            // 英文标题
            if !item.mediaItem.englishName.isEmpty {
                Text(item.mediaItem.englishName.uppercased())
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .tracking(2)
                    .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
            }

            // 分类和评分
            HStack(spacing: 16) {
                // 分类标签
                Text(item.mediaItem.category)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.white.opacity(0.2))
                    .cornerRadius(15)

                // 评分
                if item.mediaItem.formattedScore > 0 {
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.system(size: 12))
                        Text(String(format: "%.1f", item.mediaItem.formattedScore))
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.black.opacity(0.3))
                    .cornerRadius(15)
                }
            }

            // 描述
            Text(item.mediaItem.description)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .lineLimit(3)
                .padding(.horizontal, 40)
                .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)

            Spacer()
        }
    }
    
    private var pageIndicator: some View {
        HStack(spacing: 12) {
            ForEach(0..<items.count, id: \.self) { index in
                Circle()
                    .fill(index == currentIndex ? Color.white : Color.white.opacity(0.3))
                    .frame(width: 8, height: 8)
                    .animation(.easeInOut(duration: 0.3), value: currentIndex)
            }
        }
        .padding(.top, 16)
    }
    
    private var topNavigation: some View {
        VStack {
            HStack {
                Spacer()
                
                Button(action: {}) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(Color.white.opacity(0.2))
                        .clipShape(Circle())
                }
                
                Button(action: {}) {
                    Circle()
                        .fill(Color.white.opacity(0.2))
                        .frame(width: 40, height: 40)
                        .overlay(
                            Image(systemName: "person.fill")
                                .font(.system(size: 18))
                                .foregroundColor(.white)
                        )
                        .overlay(
                            Circle().stroke(Color.white, lineWidth: 2)
                        )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 50)
            
            Spacer()
        }
    }
    
    private var contentSection: some View {
        VStack(spacing: 20) {
            ForEach(0..<10) { _ in
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.1))
                    .frame(height: 100)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.top, 20)
        .background(Color(hex: "#1a4b73"))
    }
    
    private func calculateScale() -> CGFloat {
        if dragOffset < 0 {
            // 下拉时放大效果
            let pullDistance = abs(dragOffset)
            let maxPull: CGFloat = 100  // 减小阈值，让效果更容易触发
            let normalizedPull = min(pullDistance / maxPull, 1.0)
            let scaleIncrease = normalizedPull * 0.5  // 增加缩放幅度
            return 1 + scaleIncrease
        }
        return 1.0
    }
}

#Preview {
    let mockManager = MockDataManager()
    return SimpleCarouselView(items: mockManager.featuredItems)
}
