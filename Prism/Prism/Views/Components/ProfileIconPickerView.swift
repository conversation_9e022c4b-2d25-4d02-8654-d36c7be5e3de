//
//  ProfileIconPickerView.swift
//  Prism
//
//  Created by AI Assistant on 2025-07-30.
//

import SwiftUI

struct ProfileIconPickerView: View {
    @ObservedObject var iconManager: ProfileIconManager
    @Binding var isPresented: Bool
    
    let columns = [
        GridItem(.adaptive(minimum: 60), spacing: 16)
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVGrid(columns: columns, spacing: 16) {
                    ForEach(iconManager.availableIcons, id: \.self) { iconName in
                        ProfileIconItem(
                            iconName: iconName,
                            isSelected: iconManager.selectedIcon == iconName,
                            iconManager: iconManager
                        ) {
                            iconManager.selectIcon(iconName)
                            isPresented = false
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("选择头像")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        isPresented = false
                    }
                }
            }
        }
    }
}

struct ProfileIconItem: View {
    let iconName: String
    let isSelected: Bool
    let iconManager: ProfileIconManager
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Group {
                if let uiImage = iconManager.getIconImage(iconName) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } else {
                    // 备用图标
                    Image(systemName: "person.crop.circle.fill")
                        .font(.system(size: 30))
                        .foregroundColor(.gray)
                }
            }
            .frame(width: 60, height: 60)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 3)
            )
            .scaleEffect(isSelected ? 1.1 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ProfileIconPickerView(
        iconManager: ProfileIconManager(),
        isPresented: .constant(true)
    )
}
