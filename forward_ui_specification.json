{"app_info": {"name": "Forward - 新视界", "type": "AI视频播放器", "platform": ["iOS", "iPadOS", "Apple TV"], "design_language": "现代简约风格"}, "color_scheme": {"primary": {"background_gradient": {"type": "linear_gradient", "colors": ["#1a4b73", "#2d5a87", "#4a7ba7"], "direction": "top_to_bottom"}, "accent_blue": "#4a7ba7", "text_primary": "#FFFFFF", "text_secondary": "#B0C4DE"}, "ui_elements": {"button_background": "#FFFFFF", "button_text": "#1a4b73", "navigation_background": "rgba(26, 75, 115, 0.8)", "card_background": "rgba(255, 255, 255, 0.1)", "indicator_active": "#FFFFFF", "indicator_inactive": "rgba(255, 255, 255, 0.3)"}}, "typography": {"system_font": "SF Pro Display", "font_sizes": {"title_large": 24, "title_medium": 18, "body": 16, "caption": 14, "small": 12}, "font_weights": {"bold": 700, "semibold": 600, "medium": 500, "regular": 400}}, "layout_structure": {"status_bar": {"height": 44, "background": "transparent", "content": {"time": {"position": "leading", "color": "#FFFFFF", "font_size": 16}, "system_indicators": {"position": "trailing", "color": "#FFFFFF"}}}, "navigation_bar": {"height": 60, "background": "transparent", "elements": {"search_button": {"position": "trailing_secondary", "icon": "magnifyingglass", "background": "rgba(255, 255, 255, 0.2)", "size": 40, "corner_radius": 20}, "user_avatar": {"position": "trailing", "size": 40, "corner_radius": 20, "border_width": 2, "border_color": "#FFFFFF"}}}, "hero_section": {"height": 400, "background": {"type": "gradient_with_pattern", "gradient": "primary_background_gradient", "pattern": {"type": "diagonal_lines", "color": "rgba(255, 255, 255, 0.1)", "width": 2, "spacing": 40, "angle": 45}}, "content": {"media_poster": {"position": "center", "width": 200, "height": 120, "corner_radius": 12, "opacity": 0.8, "shadow": {"color": "rgba(0, 0, 0, 0.3)", "offset": {"x": 0, "y": 4}, "blur": 8}}, "date_label": {"position": "below_poster", "margin_top": 16, "font_size": 14, "color": "#FFFFFF", "alignment": "center"}, "description_text": {"position": "below_date", "margin_top": 8, "font_size": 16, "color": "#FFFFFF", "line_height": 1.4, "max_lines": 3, "alignment": "center", "padding_horizontal": 32}, "page_indicator": {"position": "bottom", "margin_bottom": 20, "dot_size": 8, "dot_spacing": 12, "active_color": "#FFFFFF", "inactive_color": "rgba(255, 255, 255, 0.3)"}}}, "content_sections": {"section_header": {"height": 50, "padding_horizontal": 20, "elements": {"title": {"position": "leading", "font_size": 18, "font_weight": "semibold", "color": "#FFFFFF"}, "action_button": {"position": "trailing", "background": "#FFFFFF", "text_color": "#1a4b73", "font_size": 14, "font_weight": "medium", "padding": {"horizontal": 16, "vertical": 8}, "corner_radius": 16}}}, "content_grid": {"padding_horizontal": 20, "item_spacing": 16, "items_per_row": 2, "card_style": {"corner_radius": 12, "background": "rgba(255, 255, 255, 0.1)", "shadow": {"color": "rgba(0, 0, 0, 0.2)", "offset": {"x": 0, "y": 2}, "blur": 4}, "poster": {"aspect_ratio": "16:9", "corner_radius": 8}, "metadata": {"padding": 12, "title_font_size": 14, "title_color": "#FFFFFF", "subtitle_font_size": 12, "subtitle_color": "#B0C4DE"}}}}, "bottom_navigation": {"height": 80, "background": "rgba(26, 75, 115, 0.9)", "backdrop_filter": "blur(20px)", "tabs": [{"id": "discover", "title": "发现", "icon": "house", "is_selected": true}, {"id": "following", "title": "追剧", "icon": "play.rectangle", "is_selected": false}, {"id": "settings", "title": "设置", "icon": "gearshape", "is_selected": false}], "tab_style": {"selected": {"icon_color": "#FFFFFF", "text_color": "#FFFFFF", "font_weight": "medium"}, "unselected": {"icon_color": "rgba(255, 255, 255, 0.6)", "text_color": "rgba(255, 255, 255, 0.6)", "font_weight": "regular"}}}}, "interactions": {"gestures": {"hero_swipe": {"type": "horizontal_pan", "action": "change_featured_content", "animation": "spring_smooth"}, "card_tap": {"type": "tap", "action": "navigate_to_detail", "feedback": "haptic_light"}, "search_tap": {"type": "tap", "action": "present_search_modal", "animation": "slide_up"}}, "animations": {"page_transition": {"type": "push", "duration": 0.3, "curve": "ease_in_out"}, "card_hover": {"type": "scale", "scale_factor": 1.05, "duration": 0.2}, "loading": {"type": "fade_in", "duration": 0.5}}}, "data_structure": {"featured_content": {"id": "string", "title": "string", "description": "string", "release_date": "string", "poster_url": "string", "backdrop_url": "string", "tmdb_id": "number", "type": "movie|tv_show"}, "content_section": {"title": "string", "items": ["featured_content"], "section_type": "trending_today|trending_week|recommended"}, "user_profile": {"avatar_url": "string", "username": "string", "subscription_status": "free|pro|premium"}}, "accessibility": {"voice_over": {"hero_content": "播放 {title}，{description}", "navigation_tabs": "{tab_name} 标签页", "content_cards": "{title}，{release_date}"}, "dynamic_type": "支持系统字体大小调整", "high_contrast": "支持高对比度模式"}, "technical_requirements": {"minimum_ios_version": "16.0", "supported_devices": ["iPhone", "iPad", "Apple TV"], "frameworks": ["SwiftUI", "AVFoundation", "CoreData"], "third_party_apis": ["TMDB API", "阿里云盘API"], "performance": {"launch_time": "< 2秒", "memory_usage": "< 200MB", "smooth_scrolling": "60fps"}}, "implementation_guide": {"swiftui_components": {"main_view": {"structure": "NavigationView + TabView", "background": "LinearGradient with overlay pattern", "safe_area": "ignoresSafeArea(.all, edges: .top)"}, "hero_carousel": {"component": "TabView with PageTabViewStyle", "data_binding": "@State var featuredContent: [FeaturedItem]", "auto_scroll": "Timer.publish(every: 5, on: .main, in: .common)", "gesture": "DragGesture for manual control"}, "content_grid": {"component": "LazyVGrid with GridItem(.flexible())", "async_loading": "@StateObject var imageLoader: AsyncImageLoader", "placeholder": "RoundedRectangle with shimmer effect"}, "bottom_tab_bar": {"component": "Custom TabView with selection binding", "animation": "withAnimation(.easeInOut(duration: 0.3))", "badge_support": "overlay with Circle() for notifications"}}, "networking": {"api_client": {"base_url": "https://api.themoviedb.org/3/", "authentication": "Bearer token in headers", "error_handling": "Result<T, APIError> pattern", "caching": "URLCache with custom policy"}, "image_loading": {"framework": "AsyncImage with custom placeholder", "caching": "NSCache for memory, FileManager for disk", "optimization": "WebP format support, progressive loading"}}, "data_management": {"core_data": {"entities": ["Movie", "TVShow", "User", "WatchHistory"], "relationships": "User -> WatchHistory <- Movie/TVShow", "fetch_requests": "NSFetchRequest with predicates and sort descriptors"}, "user_defaults": {"settings": "playback_quality, subtitle_language, theme_preference", "cache_policy": "image_cache_size, offline_content_limit"}}, "video_playback": {"avplayer": {"setup": "AVPlayer with AVPlayerViewController", "formats": "MP4, MKV, AVI support via custom decoder", "hdr_support": "AVPlayerItem.videoComposition for HDR10/Dolby Vision", "subtitles": "AVMediaSelectionGroup for subtitle tracks"}, "controls": {"custom_ui": "Overlay views with fade in/out animations", "gestures": "Tap for play/pause, swipe for seek, pinch for zoom", "picture_in_picture": "AVPictureInPictureController integration"}}}, "design_tokens": {"spacing": {"xs": 4, "sm": 8, "md": 16, "lg": 24, "xl": 32, "xxl": 48}, "border_radius": {"small": 8, "medium": 12, "large": 16, "circle": 50}, "shadows": {"light": {"color": "rgba(0, 0, 0, 0.1)", "offset": {"x": 0, "y": 2}, "blur": 4}, "medium": {"color": "rgba(0, 0, 0, 0.2)", "offset": {"x": 0, "y": 4}, "blur": 8}, "heavy": {"color": "rgba(0, 0, 0, 0.3)", "offset": {"x": 0, "y": 8}, "blur": 16}}, "animations": {"fast": 0.2, "normal": 0.3, "slow": 0.5, "spring": {"response": 0.5, "damping": 0.8, "blend_duration": 0.2}}}}